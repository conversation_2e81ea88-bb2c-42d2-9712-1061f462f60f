# 统计信息接口文档

## 签名规则说明

**接口地址**:
https://nplscx.np.gov.cn/api/

**appKey**: 87965432301

**appSecret**: gO4qBTQSirpFXeU2Hth

**签名算法**: MD5(appKey + appSecret + requestTime)

**示例**:
- appKey: 87965432301
- appSecret: gO4qBTQSirpFXeU2Hth
- requestTime: 1714117126210  注意：请求时间戳不可距当前时间戳超过15分钟
- sign: fa2a0055c016ec71b8133651bb3905ab

**产业链id和名称对应关系**
- {id: "1001", name: "氟新材料"},
- {id: "1001", name: "氟新材料"},
- {id: "1002", name: "白羽鸡"},
- {id: "1003", name: "竹"},
- {id: "1004", name: "水"},
- {id: "1008", name: "ES纤维"},
- {id: "1007", name: "物联网电池"},
- {id: "1005", name: "茶"},
- {id: "1006", name: "山"},
- {id: "1009", name: "固态电池"}

## 查询产业专家人才

**接口地址**:`/statistics/filtering`

**请求方式**:`POST`

**请求数据类型**:`application/json`

**响应数据类型**:`*/*`

**接口描述**: 根据条件查询产业专家人才信息

**请求示例**:

```json
{
  "appKey": 78965432101,
  "requestTime": "1755158013177",
  "sign": "2a38515b34d01a7e938bd9fb63e7711a",
  "pageNum": 1,
  "pageSize": 10,
  "keyword": "杨斌",
  "nodePath": "instance_concept_node_fluorochemical_industry"
}
```

**请求参数**:

| 参数名称                    | 参数说明               | 请求类型 | 是否必须 | 数据类型           | schema    |
|-------------------------| ------------------ | ---- | ---- |----------------| --------- |
| queryExpertsBO          | queryExpertsBO     | body | true | 专家查询条件         | 专家查询条件    |
| &emsp;&emsp;appKey      | 登录认证key            |      | true | long           |           |
| &emsp;&emsp;requestTime | 当前时间戳              |      | true | string         |           |
| &emsp;&emsp;sign        | 签名值                |      | true | string         |           |
| &emsp;&emsp;pageNum     | 页码                 |      | true | integer(int32) |           |
| &emsp;&emsp;pageSize    | 每页数量               |      | true | integer(int32) |           |
| &emsp;&emsp;keyword     | 关键词                |      | false | string         |           |
| &emsp;&emsp;nodePath    | 节点路径               |      | false | string         |           |
| &emsp;&emsp;requestTime | 当前时间戳              |      | true | string         |           |
| &emsp;&emsp;sign        | 签名值                |      | true | string         |           |

**响应状态**:

| 状态码 | 说明           | schema         |
| --- | ------------ | -------------- |
| 200 | OK           | 通用返回数据«专家列表» |
| 401 | Unauthorized |                |
| 403 | Forbidden    |                |
| 404 | Not Found    |                |

**响应参数**:

| 参数名称                                      | 参数说明  | 类型             | schema  |
|-------------------------------------------|-------| -------------- |---------|
| body                                      | 数据    | object         | 专家信息    |
| header                                    | 数据头   | 通用返回数据头        | 通用返回数据头 |
| &emsp;&emsp;total                         | 总数    | integer(int64) |         |
| &emsp;&emsp;list                          | 专家列表  | array         | 专家列表    |
| &emsp;&emsp;&emsp;&emsp;name              | 姓名    | string        | 姓名      |
| &emsp;&emsp;&emsp;&emsp;organization.name | 任职单位  | string        | 任职单位    |
| &emsp;&emsp;&emsp;&emsp;desc              | 简介    | string        | 简介      |
| &emsp;&emsp;code                          | 状态码   | integer(int32) |         |
| &emsp;&emsp;message                       | 状态码描述 | string         |         |

**响应示例**:

```json
{
  "body": {
    "total": 1,
    "list": [
      {
        "chain": [
          {
            "name": "氟新材料",
            "id": "1001"
          }
        ],
        "product_node": [
          {
            "chain_id": "1001",
            "name": "乙炔",
            "id": "instance_concept_node_fluorochemical_industry-010204"
          },
          {
            "chain_id": "1001",
            "name": "硫酸",
            "id": "instance_concept_node_fluorochemical_industry-010201"
          },
          {
            "chain_id": "1001",
            "name": "氯碱",
            "id": "instance_concept_node_fluorochemical_industry-010203"
          },
          {
            "chain_id": "1001",
            "name": "锂矿石",
            "id": "instance_concept_node_fluorochemical_industry-010202"
          }
        ],
        "score_model": {
          "final_score": 74.0,
          "unit": 75.0,
          "performance": 100.0,
          "personal": 70.0,
          "manage": 25.0,
          "influence": 100.0
        },
        "match_score": {
          "final_score": 64.87,
          "performance": 76.93,
          "achievement": 59.66,
          "matched": 0,
          "cooperation": 62.33,
          "influence": 60.56
        },
        "source": [
          0
        ],
        "final_edu_degree": "博士",
        "honors": [
          {
            "level": "国家级",
            "name": "国家自然科学基金获得者",
            "name_old": "国家自然科学基金"
          }
        ],
        "research_fields": "1. 真空冶金新工艺；2．有色金属二次资源的回收；3. 稀散金乌朽霉属资源的综合高效利用；4.高纯有色金属材料的真空制备。",
        "organization": {
          "name": "昆明理工大学",
          "id": null
        },
        "name": "杨斌",
        "logo": "https://bkimg.cdn.bcebos.com/pic/0eb30f2442a7d9334b5e8d6fa74bd11373f00133?x-bce-process=image/resize,m_lfit,w_268,limit_1/format,f_jpg",
        "id": "instance_entity_expert-2fb9d4aa2fc8f9e6ada0f01394e8bd12",
        "tag": null,
        "orgs": [
          "昆明理工大学",
          "南城市学院"
        ],
        "prof_title": "教授",
        "desc": "杨斌，工学博士，教授，博士生导师，云南省中青年学术和技术带头人。现任昆明理工大学副校长。"
      }
    ],
    "pageSize": null
  },
  "header": {
    "code": 200,
    "message": "success"
  }
}
```

## 产业链-链上企业分布

**接口地址**:`/statistics/industry/company`

**请求方式**:`POST`

**请求数据类型**:`application/json`

**响应数据类型**:`*/*`

**接口描述**: 获取指定产业链上的企业分布情况

**请求示例**:

```json
{
  "appKey": 78965432101,
  "requestTime": "1755158013177",
  "sign": "2a38515b34d01a7e938bd9fb63e7711a",
  "chainId": "1003"
}
```

**请求参数**:

| 参数名称                    | 参数说明               | 请求类型 | 是否必须 | 数据类型    | schema    |
| ----------------------- | ------------------ | ---- | ---- |---------| --------- |
| chainBO                 | chainBO            | body | true | 产业链查询条件 | 产业链查询条件 |
| &emsp;&emsp;appKey      | 登录认证key            |      | true | long    |           |
| &emsp;&emsp;chainId     | 产业链ID              |      | true | string  |           |
| &emsp;&emsp;requestTime | 当前时间戳              |      | true | string  |           |
| &emsp;&emsp;sign        | 签名值                |      | true | string  |           |

**响应状态**:

| 状态码 | 说明           | schema         |
| --- | ------------ | -------------- |
| 200 | OK           | 通用返回数据«企业分布信息» |
| 401 | Unauthorized |                |
| 403 | Forbidden    |                |
| 404 | Not Found    |                |

**响应参数**:

| 参数名称                                      | 参数说明  | 类型             | schema  |
|-------------------------------------------| ----- |----------------| ------- |
| body                                      | 数据    | object         | 企业分布信息  |
| &emsp;&emsp;companyNum                    | 链上企业数量 | integer        |         |
| &emsp;&emsp;nodeNum                       | 布局产业链节点 | integer        |         |
| &emsp;&emsp;typeCount                     | 企业类型统计 | object         |         |
| &emsp;&emsp;&emsp;&emsp;totalNum          | 企业总数 | integer        |         |
| &emsp;&emsp;&emsp;&emsp;middleAndSmallNum | 科技型中小企业总数 | integer        |         |
| &emsp;&emsp;&emsp;&emsp;middleAndSmallRate | 科技型中小企业比例 | float          |         |
| &emsp;&emsp;&emsp;&emsp;highTechNum           | 高新技术企业总数 | integer        |         |
| &emsp;&emsp;&emsp;&emsp;highTechRate           | 高新技术企业比例 | float        |         |
| &emsp;&emsp;&emsp;&emsp;giantNum           | 科技小巨人企业总数 | integer        |         |
| &emsp;&emsp;&emsp;&emsp;giantNumRate           | 科技小巨人企业比例 | float        |         |
| &emsp;&emsp;&emsp;&emsp;leadNum           | 科技领军企业总数 | integer        |         |
| &emsp;&emsp;&emsp;&emsp;leadNumRate           | 科技领军企业比例 | float        |         |
| header                                    | 数据头   | 通用返回数据头        | 通用返回数据头 |
| &emsp;&emsp;code                          | 状态码   | integer(int32) |         |
| &emsp;&emsp;message                       | 状态码描述 | string         |         |

**响应示例**:

```json
{
  "body": {
    "companyNum": 919,
    "nodeNum": 82,
    "typeCount": {
      "totalNum": 919,
      "middleAndSmallNum": 49,
      "middleAndSmallRate": 5.3300,
      "highTechNum": 44,
      "highTechRate": 4.7900,
      "giantNum": 42,
      "giantNumRate": 4.5700,
      "leadNum": 0,
      "leadNumRate": 0.0000
    }
  },
  "header": {
    "code": 200,
    "message": "success"
  }
}
```

## 产业链-链节点分布-圆环图统计

**接口地址**:`/statistics/industry/node/distribution`

**请求方式**:`POST`

**请求数据类型**:`application/json`

**响应数据类型**:`*/*`

**接口描述**: 获取产业链节点分布的圆环图统计数据

**请求示例**:

```json
{
  "appKey": 78965432101,
  "requestTime": "1755158013177",
  "sign": "2a38515b34d01a7e938bd9fb63e7711a",
  "chainId": "1003"
}
```

**请求参数**:

| 参数名称                    | 参数说明               | 请求类型 | 是否必须 | 数据类型    | schema    |
| ----------------------- | ------------------ | ---- | ---- |---------| --------- |
| chainBO                 | chainBO            | body | true | 产业链查询条件 | 产业链查询条件 |
| &emsp;&emsp;appKey      | 登录认证key            |      | true | long    |           |
| &emsp;&emsp;chainId     | 产业链ID              |      | true | string  |           |
| &emsp;&emsp;requestTime | 当前时间戳              |      | true | string  |           |
| &emsp;&emsp;sign        | 签名值                |      | true | string  |           |

**响应状态**:

| 状态码 | 说明           | schema         |
| --- | ------------ | -------------- |
| 200 | OK           | 通用返回数据«PieVO» |
| 401 | Unauthorized |                |
| 403 | Forbidden    |                |
| 404 | Not Found    |                |

**响应参数**:

| 参数名称                   | 参数说明  | 类型             | schema  |
| ---------------------- |-------| -------------- | ------- |
| body                   | 数据    | object         | PieVO   |
| &emsp;&emsp;total       | 总数    | integer          |  |
| &emsp;&emsp;indexList       | 明细    | array          |  |
| &emsp;&emsp;&emsp;&emsp;id       | id    | string          |  |
| &emsp;&emsp;&emsp;&emsp;name       | 名称    | string          |  |
| &emsp;&emsp;&emsp;&emsp;data       | 数值    | integer          |  |
| &emsp;&emsp;&emsp;&emsp;unit       | 单位    | string          |  |
| header                 | 数据头   | 通用返回数据头        |  |
| &emsp;&emsp;code       | 状态码   | integer(int32) |         |
| &emsp;&emsp;message    | 状态码描述 | string         |         |

**响应示例**:

```json
{
  "body": {
    "indexList": [
      {
        "id": "1",
        "name": "强链",
        "data": 9,
        "unit": "个"
      },
      {
        "id": "2",
        "name": "补链",
        "data": 7,
        "unit": "个"
      },
      {
        "id": "3",
        "name": "固链",
        "data": 5,
        "unit": "个"
      },
      {
        "id": "4",
        "name": "拓链",
        "data": 10,
        "unit": "个"
      }
    ],
    "total": 31
  },
  "header": {
    "code": 200,
    "message": "success"
  }
}
```

## 人才学历、职称分布

**接口地址**:`/statistics/degree_title/distribution`

**请求方式**:`POST`

**请求数据类型**:`application/json`

**响应数据类型**:`*/*`

**接口描述**: 获取指定产业链的人才学历和职称分布统计

**请求示例**:

```json
{
  "appKey": 78965432101,
  "requestTime": "1755159168859",
  "sign": "100ff73bbb20a463a13270f08d0e12a7",
  "chainId": "1003"
}
```

**请求参数**:

| 参数名称                    | 参数说明               | 请求类型 | 是否必须 | 数据类型    | schema    |
| ----------------------- | ------------------ | ---- | ---- |---------| --------- |
| chainBO                 | chainBO            | body | true | 产业链查询条件 | 产业链查询条件 |
| &emsp;&emsp;appKey      | 登录认证key            |      | true | long    |           |
| &emsp;&emsp;chainId     | 产业链ID              |      | true | string  |           |
| &emsp;&emsp;requestTime | 当前时间戳              |      | true | string  |           |
| &emsp;&emsp;sign        | 签名值                |      | true | string  |           |

**响应状态**:

| 状态码 | 说明           | schema         |
| --- | ------------ | -------------- |
| 200 | OK           | 通用返回数据«学历职称分布» |
| 401 | Unauthorized |                |
| 403 | Forbidden    |                |
| 404 | Not Found    |                |

**响应参数**:

| 参数名称                  | 参数说明  | 类型             | schema  |
| --------------------- | ----- | -------------- | ------- |
| body                  | 数据    | object         | 学历职称分布  |
| &emsp;&emsp;degree| 学历分布 | array | 学历分布项 |
| &emsp;&emsp;title | 职称分布 | array | 职称分布项 |
| &emsp;&emsp;&emsp;&emsp;total       | 总数    | integer          |  |
| &emsp;&emsp;&emsp;&emsp;indexList       | 明细    | array          |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id       | id    | string          |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;name       | 名称    | string          |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;data       | 数值    | integer          |  |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;unit       | 单位    | string          |  |
| header                | 数据头   | 通用返回数据头        | 通用返回数据头 |
| &emsp;&emsp;code      | 状态码   | integer(int32) |         |
| &emsp;&emsp;message   | 状态码描述 | string         |         |

**响应示例**:

```json
{
  "body": {
    "degree": {
      "indexList": [
        {
          "id": null,
          "name": "博士",
          "data": 4908,
          "unit": "人"
        },
        {
          "id": null,
          "name": "硕士",
          "data": 466,
          "unit": "人"
        },
        {
          "id": null,
          "name": "学士",
          "data": 54,
          "unit": "人"
        },
        {
          "id": null,
          "name": "其他",
          "data": 286,
          "unit": "人"
        }
      ],
      "total": 5714
    },
    "title": {
      "indexList": [
        {
          "id": null,
          "name": "教授",
          "data": 2291,
          "unit": "人"
        },
        {
          "id": null,
          "name": "副教授",
          "data": 2102,
          "unit": "人"
        },
        {
          "id": null,
          "name": "研究员",
          "data": 528,
          "unit": "人"
        },
        {
          "id": null,
          "name": "副研究员",
          "data": 241,
          "unit": "人"
        },
        {
          "id": null,
          "name": "高级工程师",
          "data": 120,
          "unit": "人"
        },
        {
          "id": null,
          "name": "其他",
          "data": 448,
          "unit": "人"
        }
      ],
      "total": 5730
    }
  },
  "header": {
    "code": 200,
    "message": "success"
  }
}
```

## 外部人才节点分布

**接口地址**:`/statistics/external/node/distribution`

**请求方式**:`POST`

**请求数据类型**:`application/json`

**响应数据类型**:`*/*`

**接口描述**: 获取外部人才在各节点的分布情况

**请求示例**:

```json
{
  "appKey": 78965432101,
  "requestTime": "1755159168859",
  "sign": "100ff73bbb20a463a13270f08d0e12a7",
  "chainId": "1003"
}
```

**请求参数**:

| 参数名称                    | 参数说明               | 请求类型 | 是否必须 | 数据类型    | schema    |
| ----------------------- | ------------------ | ---- | ---- |---------| --------- |
| chainBO                 | chainBO            | body | true | 产业链查询条件 | 产业链查询条件 |
| &emsp;&emsp;appKey      | 登录认证key            |      | true | long    |           |
| &emsp;&emsp;chainId     | 产业链ID              |      | true | string  |           |
| &emsp;&emsp;requestTime | 当前时间戳              |      | true | string  |           |
| &emsp;&emsp;sign        | 签名值                |      | true | string  |           |

**响应状态**:

| 状态码 | 说明           | schema         |
| --- | ------------ | -------------- |
| 200 | OK           | 通用返回数据«PieVO» |
| 401 | Unauthorized |                |
| 403 | Forbidden    |                |
| 404 | Not Found    |                |

**响应参数**:

| 参数名称                   | 参数说明  | 类型             | schema  |
| ---------------------- | ----- | -------------- | ------- |
| body                   | 数据    | object         | PieVO   |
| &emsp;&emsp;total       | 总数    | integer          |  |
| &emsp;&emsp;indexList       | 明细    | array          |  |
| &emsp;&emsp;&emsp;&emsp;id       | id    | string          |  |
| &emsp;&emsp;&emsp;&emsp;name       | 名称    | string          |  |
| &emsp;&emsp;&emsp;&emsp;data       | 数值    | integer          |  |
| &emsp;&emsp;&emsp;&emsp;unit       | 单位    | string          |  |
| header                 | 数据头   | 通用返回数据头        | 通用返回数据头 |
| &emsp;&emsp;code       | 状态码   | integer(int32) |         |
| &emsp;&emsp;message    | 状态码描述 | string         |         |

**响应示例**:

```json
{
  "body": {
    "indexList": [
      {
        "id": null,
        "name": "竹机械",
        "data": 1314,
        "unit": "人"
      },
      {
        "id": null,
        "name": "竹产品",
        "data": 1194,
        "unit": "人"
      },
      {
        "id": null,
        "name": "竹资源培育",
        "data": 1097,
        "unit": "人"
      },
      {
        "id": null,
        "name": "竹衍生业态",
        "data": 518,
        "unit": "人"
      },
      {
        "id": null,
        "name": "竹创新业态",
        "data": 364,
        "unit": "人"
      },
      {
        "id": null,
        "name": "其他",
        "data": 1590,
        "unit": "人"
      }
    ],
    "total": 5427
  },
  "header": {
    "code": 200,
    "message": "success"
  }
}
```

## 投融资指标监测

**接口地址**:`/statistics/get/finance/info`

**请求方式**:`POST`

**请求数据类型**:`application/json`

**响应数据类型**:`*/*`

**接口描述**: 获取指定产业链的投融资指标监测数据

**请求示例**:

```json
{
  "appKey": 78965432101,
  "requestTime": "1755159168859",
  "sign": "100ff73bbb20a463a13270f08d0e12a7",
  "chainId": "1003"
}
```

**请求参数**:

| 参数名称                    | 参数说明               | 请求类型 | 是否必须 | 数据类型    | schema    |
| ----------------------- | ------------------ | ---- | ---- |---------| --------- |
| chainBO                 | chainBO            | body | true | 产业链查询条件 | 产业链查询条件 |
| &emsp;&emsp;appKey      | 登录认证key            |      | true | long    |           |
| &emsp;&emsp;chainId     | 产业链ID              |      | true | string  |           |
| &emsp;&emsp;requestTime | 当前时间戳              |      | true | string  |           |
| &emsp;&emsp;sign        | 签名值                |      | true | string  |           |

**响应状态**:

| 状态码 | 说明           | schema         |
| --- | ------------ | -------------- |
| 200 | OK           | 通用返回数据«投融资信息» |
| 401 | Unauthorized |                |
| 403 | Forbidden    |                |
| 404 | Not Found    |                |

**响应参数**:

| 参数名称                   | 参数说明   | 类型             | schema  |
| ---------------------- |--------|----------------| ------- |
| body                   | 数据     | object         | 投融资信息   |
| &emsp;&emsp;evaluation | 指标分析建议 | string         | |
| &emsp;&emsp;subsidyNum | 项目补助笔数 | integer        | |
| &emsp;&emsp;chainName | 产业链名称  | string         | |
| &emsp;&emsp;loanNum | 贷款企业数量 | string         | |
| &emsp;&emsp;financeNum | 股权融资笔数 | integer        | |
| &emsp;&emsp;totalFinanceAmount | 投融资金额  | float         | |
| &emsp;&emsp;financeAmount | 风险资金   | float         | |
| &emsp;&emsp;subsidyIncreaseRate | 项目补助增幅  | float        | |
| &emsp;&emsp;subsidyAmount | 项目补助资金   | integer | |
| &emsp;&emsp;loanAmount | 贷款金额 | float          | |
| header                 | 数据头    | 通用返回数据头        | 通用返回数据头 |
| &emsp;&emsp;code       | 状态码    | integer(int32) |         |
| &emsp;&emsp;message    | 状态码描述  | string         |         |

**响应示例**:

```json
{
  "body": {
    "evaluation": "南平竹产业发展得到了金融机构的大力支持，已设立产业绿色金融信贷风险资金池，鼓励金融机构扩大竹产业贷款规模。南平竹产业在融资总额、融资成本、融资约束等指标上波动较大，低于闽浙赣均值，需进一步发展绿色信贷产品，增大竹产业二产的资金投入，积极招商引资。",
    "subsidyNum": 31,
    "chainName": "竹",
    "loanNum": 16,
    "financeNum": 555,
    "totalFinanceAmount": 25702775.27,
    "financeAmount": 25692159.27,
    "subsidyIncreaseRate": -100.00,
    "subsidyAmount": 2192,
    "loanAmount": 10616.00
  },
  "header": {
    "code": 200,
    "message": "success"
  }
}
```

## 项目补助列表

**接口地址**:`/statistics/get/subsidy/list`

**请求方式**:`POST`

**请求数据类型**:`application/json`

**响应数据类型**:`*/*`

**接口描述**: 获取项目补助列表信息

**请求示例**:

```json
{
  "appKey": 78965432101,
  "requestTime": "1755159168859",
  "sign": "100ff73bbb20a463a13270f08d0e12a7",
  "chainId": "1003",
  "pageNum": 1,
  "pageSize": 10,
  "companyName": "夷山",
  "projectName": "应用"
}
```

**请求参数**:

| 参数名称                    | 参数说明               | 请求类型 | 是否必须 | 数据类型           | schema    |
| ----------------------- | ------------------ | ---- | ---- | -------------- | --------- |
| subsidyBO               | subsidyBO          | body | true | 补助查询条件         | 补助查询条件  |
| &emsp;&emsp;appKey      | 登录认证key            |      | true | long |           |
| &emsp;&emsp;chainId     | 产业链ID              |      | true | string         |           |
| &emsp;&emsp;pageNum     | 页码                 |      | true | integer(int32) |           |
| &emsp;&emsp;pageSize    | 每页数量               |      | true | integer(int32) |           |
| &emsp;&emsp;companyName | 企业名称               |      | false | string         |           |
| &emsp;&emsp;projectName | 项目名称               |      | false | string         |           |
| &emsp;&emsp;requestTime | 当前时间戳              |      | true | string         |           |
| &emsp;&emsp;sign        | 签名值                |      | true | string         |           |

**响应状态**:

| 状态码 | 说明           | schema         |
| --- | ------------ | -------------- |
| 200 | OK           | 通用返回数据«补助列表» |
| 401 | Unauthorized |                |
| 403 | Forbidden    |                |
| 404 | Not Found    |                |

**响应参数**:

| 参数名称                                                 | 参数说明     | 类型            | schema  |
|------------------------------------------------------|----------|---------------|---------|
| body                                                 | 数据       | object        | 补助列表    |
| &emsp;&emsp;total                                    | 总数       | integer(int64) |         |
| &emsp;&emsp;list                                     | 补助项目列表   | array         | 补助项目    |
| &emsp;&emsp;&emsp;&emsp;name                         | 项目名称     | string        | 项目名称    |
| &emsp;&emsp;&emsp;&emsp;apply_expense       | 补助金额（万元） | float         | 补助金额    |
| &emsp;&emsp;&emsp;&emsp;acceptance_date          | 时间       | string        | 时间      |
| &emsp;&emsp;&emsp;&emsp;undertaking_unit             | 企业       | object        | 企业      |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id   | 企业id     | string        | id      |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;name | 企业名称     | string        | 名称      |
| header                                               | 数据头      | 通用返回数据头       | 通用返回数据头 |
| &emsp;&emsp;code                                     | 状态码      | integer(int32) |         |
| &emsp;&emsp;message                                  | 状态码描述    | string        |         |

**响应示例**:

```json
{
  "body": {
    "total": 1,
    "list": [
      {
        "project_type": [
          "福建省科技特派员后补助项目"
        ],
        "apply_expense": 25.0,
        "name": "竹笋预制菜加工关键技术集成创新与产业化应用",
        "undertaking_unit": {
          "name": "福建夷山物产食品实业有限公司",
          "id": "instance_entity_company-c8ce8632940f8f51b961c8b83bf4f126",
          "tag": [
            {
              "approved_year": "2022",
              "name": "高新技术企业",
              "id": "3"
            },
            {
              "approved_year": "2023",
              "name": "科技型中小企业",
              "id": "4"
            },
            {
              "approved_year": "2021",
              "name": "科技小巨人",
              "id": "2"
            },
            {
              "approved_year": "2023",
              "name": "加计扣除优惠企业",
              "id": null
            }
          ]
        },
        "acceptance_date": "2024-01-01",
        "start_date": "2024"
      }
    ],
    "pageSize": null
  },
  "header": {
    "code": 200,
    "message": "success"
  }
}
```

## 关键核心技术分类统计

**接口地址**:`/statistics/weak/type`

**请求方式**:`POST`

**请求数据类型**:`application/json`

**响应数据类型**:`*/*`

**接口描述**: 获取关键核心技术的分类统计信息

**请求示例**:

```json
{
  "appKey": 78965432101,
  "requestTime": "1755159168859",
  "sign": "100ff73bbb20a463a13270f08d0e12a7",
  "chainId": "1003"
}
```

**请求参数**:

| 参数名称                    | 参数说明               | 请求类型 | 是否必须 | 数据类型    | schema    |
| ----------------------- | ------------------ | ---- | ---- |---------| --------- |
| chainBO                 | chainBO            | body | true | 产业链查询条件 | 产业链查询条件 |
| &emsp;&emsp;appKey      | 登录认证key            |      | true | long    |           |
| &emsp;&emsp;chainId     | 产业链ID              |      | true | string  |           |
| &emsp;&emsp;requestTime | 当前时间戳              |      | true | string  |           |
| &emsp;&emsp;sign        | 签名值                |      | true | string  |           |

**响应状态**:

| 状态码 | 说明           | schema         |
| --- | ------------ | -------------- |
| 200 | OK           | 通用返回数据«List«CommonIndexBO»» |
| 401 | Unauthorized |                |
| 403 | Forbidden    |                |
| 404 | Not Found    |                |

**响应参数**:

| 参数名称                   | 参数说明   | 类型             | schema  |
| ---------------------- |--------| -------------- | ------- |
| body                   | 数据     | array          | CommonIndexBO |
| &emsp;&emsp;name       | 技术分类名称 | string | |
| &emsp;&emsp;data      | 统计值    | number | |
| &emsp;&emsp;unit | 单位     | string | |
| header                 | 数据头    | 通用返回数据头        | 通用返回数据头 |
| &emsp;&emsp;code       | 状态码    | integer(int32) |         |
| &emsp;&emsp;message    | 状态码描述  | string         |         |

**响应示例**:

```json
{
  "body": [
    {
      "id": null,
      "name": "市内有基础实现攻关突破",
      "data": 20,
      "unit": null
    },
    {
      "id": null,
      "name": "省内有基础实现攻关突破",
      "data": 8,
      "unit": null
    },
    {
      "id": null,
      "name": "已实现国产化替代",
      "data": 3,
      "unit": null
    }
  ],
  "header": {
    "code": 200,
    "message": "success"
  }
}
```

## 技术合同成果交易统计

**接口地址**:`/statistics/ach_trade/count`

**请求方式**:`POST`

**请求数据类型**:`application/json`

**响应数据类型**:`*/*`

**接口描述**: 获取技术合同成果交易的统计数据

**请求示例**:

```json
{
  "appKey": 78965432101,
  "requestTime": "1755159168859",
  "sign": "100ff73bbb20a463a13270f08d0e12a7"
}
```

**请求参数**:

| 参数名称                    | 参数说明               | 请求类型 | 是否必须 | 数据类型      | schema    |
| ----------------------- | ------------------ | ---- | ---- |-----------| --------- |
| baseThirdQuery          | baseThirdQuery     | body | true | 基础第三方查询条件 | 基础第三方查询条件 |
| &emsp;&emsp;appKey      | 登录认证key            |      | true | long      |           |
| &emsp;&emsp;requestTime | 当前时间戳              |      | true | string    |           |
| &emsp;&emsp;sign        | 签名值                |      | true | string    |           |

**响应状态**:

| 状态码 | 说明           | schema         |
| --- | ------------ | -------------- |
| 200 | OK           | 通用返回数据«交易统计» |
| 401 | Unauthorized |                |
| 403 | Forbidden    |                |
| 404 | Not Found    |                |

**响应参数**:

| 参数名称                                            | 参数说明  | 类型             | schema  |
|-------------------------------------------------|-------|----------------| ------- |
| body                                            | 数据    | object         | 交易统计    |
| &emsp;&emsp;totalCount                          | 总交易数量 | integer(int32) | |
| &emsp;&emsp;totalAmount                         | 总交易金额 | number         | |
| &emsp;&emsp;indexList                           | 类型分布  | array          | 类型统计 |
| &emsp;&emsp;&emsp;&emsp;name                    | 交易类别  | string | |
| &emsp;&emsp;&emsp;&emsp;childList               | 指标列表  | array          | 类型统计 |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;name  | 指标名称  | string         | |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;data | 数值    | float          | |
| &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;unit | 单位    | string         | |
| header                                          | 数据头   | 通用返回数据头        | 通用返回数据头 |
| &emsp;&emsp;code                                | 状态码   | integer(int32) |         |
| &emsp;&emsp;message                             | 状态码描述 | string         |         |

**响应示例**:

```json
{
  "body": {
    "totalAmount": "23624.34",
    "totalCount": 10,
    "indexList": [
      {
        "name": "技术开发",
        "data": null,
        "childList": [
          {
            "id": null,
            "name": "contractNum",
            "data": 2,
            "unit": null
          },
          {
            "id": null,
            "name": "contractAmount",
            "data": "9454.37",
            "unit": null
          }
        ]
      },
      {
        "name": "技术转让",
        "data": null,
        "childList": [
          {
            "id": null,
            "name": "contractNum",
            "data": 3,
            "unit": null
          },
          {
            "id": null,
            "name": "contractAmount",
            "data": "7043.91",
            "unit": null
          }
        ]
      },
      {
        "name": "技术许可",
        "data": null,
        "childList": [
          {
            "id": null,
            "name": "contractNum",
            "data": 1,
            "unit": null
          },
          {
            "id": null,
            "name": "contractAmount",
            "data": "543.29",
            "unit": null
          }
        ]
      },
      {
        "name": "技术服务",
        "data": null,
        "childList": [
          {
            "id": null,
            "name": "contractNum",
            "data": 3,
            "unit": null
          },
          {
            "id": null,
            "name": "contractAmount",
            "data": "5686.23",
            "unit": null
          }
        ]
      },
      {
        "name": "技术咨询",
        "data": null,
        "childList": [
          {
            "id": null,
            "name": "contractNum",
            "data": 1,
            "unit": null
          },
          {
            "id": null,
            "name": "contractAmount",
            "data": "896.54",
            "unit": null
          }
        ]
      }
    ]
  },
  "header": {
    "code": 200,
    "message": "success"
  }
}
```

## 创新链诊断

**接口地址**:`/statistics/chain_analyze`

**请求方式**:`POST`

**请求数据类型**:`application/json`

**响应数据类型**:`*/*`

**接口描述**: 对指定产业链进行创新链诊断分析

**请求示例**:

```json
{
  "appKey": 78965432101,
  "requestTime": "1755159168859",
  "sign": "100ff73bbb20a463a13270f08d0e12a7",
  "chainId": "1003"
}
```

**请求参数**:

| 参数名称                    | 参数说明               | 请求类型 | 是否必须 | 数据类型    | schema    |
| ----------------------- | ------------------ | ---- | ---- |---------| --------- |
| chainBO                 | chainBO            | body | true | 产业链查询条件 | 产业链查询条件 |
| &emsp;&emsp;appKey      | 登录认证key            |      | true | long     |           |
| &emsp;&emsp;chainId     | 产业链ID              |      | true | string  |           |
| &emsp;&emsp;requestTime | 当前时间戳              |      | true | string  |           |
| &emsp;&emsp;sign        | 签名值                |      | true | string  |           |

**响应状态**:

| 状态码 | 说明           | schema         |
| --- | ------------ | -------------- |
| 200 | OK           | 通用返回数据«创新链诊断» |
| 401 | Unauthorized |                |
| 403 | Forbidden    |                |
| 404 | Not Found    |                |

**响应参数**:

| 参数名称                   | 参数说明   | 类型             | schema  |
| ---------------------- |--------| -------------- |---------|
| body                   | 数据     | object         | 创新链诊断   |
| &emsp;&emsp;overview | 整体情况   | array | 整体情况    |
| &emsp;&emsp;&emsp;&emsp;name | 指标名称   | string | 指标名称    |
| &emsp;&emsp;&emsp;&emsp;data | 指标数值   | object | 指标数值    |
| &emsp;&emsp;&emsp;&emsp;unit | 单位     | string | 单位      |
| &emsp;&emsp;weaknesses | 关键核心技术 | array | 关键核心技术  |
| &emsp;&emsp;&emsp;&emsp;name | 技术名称   | string | 名称      |
| &emsp;&emsp;&emsp;&emsp;dataId | 技术id   | string | id      |
| &emsp;&emsp;&emsp;&emsp;techDesc | 技术简介   | string | 简介      |
| header                 | 数据头    | 通用返回数据头        | 通用返回数据头 |
| &emsp;&emsp;code       | 状态码    | integer(int32) |         |
| &emsp;&emsp;message    | 状态码描述  | string         |         |

**响应示例**:

```json
{
  "body": {
    "overview": [
      {
        "id": null,
        "name": "创新链指数",
        "data": null,
        "unit": "分"
      },
      {
        "id": null,
        "name": "链上节点",
        "data": 96,
        "unit": "个"
      },
      {
        "id": null,
        "name": "链上企业",
        "data": 6248,
        "unit": "家"
      },
      {
        "id": null,
        "name": "链上人才",
        "data": 5730,
        "unit": "人"
      },
      {
        "id": null,
        "name": "链上成果",
        "data": 465,
        "unit": "项"
      },
      {
        "id": null,
        "name": "链上专利",
        "data": 10000,
        "unit": "项"
      }
    ],
    "weaknesses": [
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "竹木智能化精加工技术",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "高端竹业装备存在“卡脖子”技术，依赖日本等国家进口；加快竹材采伐运输、前序工段、连续成型等关键技术装备产业化，可显著提高生产效率，降低成本，稳定产品质量，提升市场竞争力。《全国竹产业发展规划（2021—2030年）》要求到 2030 年，竹材加工机械化率达 80%~90%",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "963102ef3bb3774111b6bb8df6cbfad0",
        "techDesc": "竹木智能化精加工技术通过人工智能算法、数字图像分析技术和自动化技术，大幅提升竹木生产企业在上料及竹木分选生产环节的效率，包括全自动化智能上料装置与智能竹条精选机器人，实现全自动上料，通过AI技术自动过滤劣质材料，保障原材料品质，并避免材料的浪费;对有缺陷的竹条自动进行分类，无缺陷的竹条分色。解决竹企招不到生产线工人，竹条人工检测效率低，工人检测的误检、漏检率高等问题，大幅度节约生产成本提高生产效率，为竹木行业的智能化升级提供整体解决方案.",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "木(竹)材节能热加工国家创新联盟",
            "id": null
          },
          {
            "name": "浙江省林业科学研究院竹类(智能装备)",
            "id": null
          },
          {
            "name": "北京林业大学",
            "id": null
          },
          {
            "name": "龙泉市大展竹科技有限责任公司",
            "id": "instance_entity_company-db11e7e0a8c11f531ec350ad693b738a"
          },
          {
            "name": "石家庄灿高高频机械有限公司",
            "id": "instance_entity_company-43f1ee216c274486db65582ede928cf2"
          },
          {
            "name": "济南全劲试验机有限公司",
            "id": null
          },
          {
            "name": "安吉前程机械有限公司",
            "id": null
          },
          {
            "name": "安吉吉泰机械有限公司",
            "id": "instance_entity_company-501340ca87fed7650c167690e6295bf7"
          },
          {
            "name": "国家林业和草原局北京林业机械研究所，湖南省林业科学院",
            "id": null
          },
          {
            "name": "南京林业大学",
            "id": null
          },
          {
            "name": "安吉八塔机器人有限公司",
            "id": "instance_entity_company-d26382da485fca6c26dad59db5a2d05c"
          },
          {
            "name": "东莞市嘉航实业有限公司",
            "id": "instance_entity_company-baa5a55b21641549501edb7ea8d730f0"
          },
          {
            "name": "中国福马机械集团有限公司",
            "id": "instance_entity_company-f84a364b6074591746a51d9aa986f8fe"
          }
        ],
        "sort": 4888
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "高性能竹机运输装备",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "竹山运输一直是制约竹产业发展的瓶颈问题。为解决竹材山林运输困难和农村劳动力短缺等难题，加快提升竹林运输机械化水平，可大大提高竹材运输效率，降低劳动成本，实现竹材高效、低成本运输。《全国竹产业发展规划（2021—2030年）》要求到 2030 年，竹材采伐运输机械化率达 40%~50%",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "222a5bfb68c07d2df2115a9e3d7b1ab6",
        "techDesc": "由于竹子主要生长在丘陵山地，砍伐运输劳动强度大、开采效率低、运输成本高，要打通竹子精深加工的“上半程”，客观上对竹子砍伐、运输机械化提出了更高要求。为应对丘陵山地竹林的砍伐与运输挑战，研发了丘陵山地竹林单轨运输机与采用重载无人机运输技术。单轨运输机专为复杂地形设计，极大提升了采伐效率，减少人工劳动强度，与传统人工相比，机械工作效率提升超过8倍，该设备具有占地面积小、操作简便、性能安全稳定等优点，同时维护和使用成本较低。重载无人机的引入，为竹林运输提供了高效、低成本的解决方案。高性能竹机运输装备的创新应用为竹子下山提供了新的运输途径，将极大提高竹资源开发效率，推动竹区经济发展，市场前景广阔。",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "浙江省林业科学研究院竹类(智能装备)",
            "id": null
          },
          {
            "name": "中国林业科学研究院木材工业研究所",
            "id": null
          },
          {
            "name": "中国福马机械集团有限公司",
            "id": "instance_entity_company-f84a364b6074591746a51d9aa986f8fe"
          },
          {
            "name": "益阳竟宁竹业机器人有限公司",
            "id": "instance_entity_company-867e097172944b6c22ffffb8d79a8a80"
          }
        ],
        "sort": 4901
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "高端竹纤维复合材料(以竹代钢)",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "专利几乎被我国独占，是中国首创的技术，领先全球；文献研究中的热点领域；国家及各地规划均提出要重点发展该技术",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "3d711f5c75ba3cd47fb81ed6e2fd845e",
        "techDesc": "高性能竹基纤维复合材料（竹钢）是以我国南方地区大量生长的竹材资源为原料，通过纤维化竹束帘制备技术、酚醛树脂均匀导入技术、连续式网带干燥技术、大幅面板坯铺装技术、成型固化技术等多项技术集成，实现竹基纤维复合材料的高性能和可调控，最终制造成高性能多用途竹基纤维复合材料。该产品技术节省了传统的剖蔑工序，是我国在竹材加工应用领域的一项重大突破，属于竹材工业化利用的第五代技术，竹材的利用率从目前的20%~50%，可以提高到95%以上。产品的力学性能指标，如抗弯强度可以达到350MPa以上，抗拉强度达360MPa以上，抗压强度达到140MPa以上，弹性模量达到30GPa以上。高性能竹基纤维复合材料（竹钢）采用纯天然慈竹经酚醛树脂热压胶合而成，具有高强度、低碳环保、高耐候性、阻燃、净化空气、使用寿命长等特点，是“以竹代木、以竹代钢”的最佳产品；是一种将竹材重新组织并加以强化成型的竹质新材料，对增加竹农的收入、促进农民就业和发展竹产区经济具有十分重要的意义。",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "中国林科院",
            "id": null
          },
          {
            "name": "国际竹藤中心",
            "id": null
          },
          {
            "name": "南京林业大学",
            "id": null
          },
          {
            "name": "国家林业和草原局重组材工程技术研究中心",
            "id": null
          },
          {
            "name": "中国林业科学研究院木材工业研究所",
            "id": null
          },
          {
            "name": "四川竹元科技有限公司",
            "id": "instance_entity_company-ea70b252a571973e0238aa905b95a005"
          }
        ],
        "sort": 4905
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "功能性竹基工程材料",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "功能性竹基工程材料在强度和韧性这两个关键的结构特性上可以良好的兼容性，具有较高的机械拉伸强度、弯曲强度和韧性，相对于其他合成高强度材料，具有环保低碳、材料丰富、可持续、可大规模生产的优点",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "6743f01b9227e52da730da550c0e0f22",
        "techDesc": "目前，我国针对竹材的研究主要集中于竹材制造工艺、竹木重组及竹塑复合等领域，先后开发了竹编胶合板、竹材集成材、竹材层积材、竹材重组材、竹材复合板等多种竹质工程材料和装饰材料，产品品种已系列化和标准化，在竹材产品开发与应用方面走在世界前列。竹材与木材相比在建筑方面的特性毫不逊色，竹材本身的抗拉强度及弯曲强度可达150MPa左右，抗压强度可达60~70MPa，弯曲弹性模量达10GPa以上。可见，竹材的力学性能优于普通木材，而且竹材有较好的弹性与韧性。现代竹质工程材料的产品形式已十分丰富与成熟，可用于建筑结构构件的竹材产品有竹材胶合板、竹材层积材、竹材重组材等。",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "湖南九通竹基复合材料制造有限公司",
            "id": "instance_entity_company-24b2c62cdae5020e6b2a26a697511b5d"
          },
          {
            "name": "江苏亨达竹格填料有限公司",
            "id": "instance_entity_company-3416b4705ae3f4ec83849289b0731321"
          },
          {
            "name": "中林集团所属中林绿碳(北京)科技有限公司",
            "id": null
          },
          {
            "name": "四川竹元科技有限公司",
            "id": "instance_entity_company-ea70b252a571973e0238aa905b95a005"
          }
        ],
        "sort": 4911
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "以竹代塑创新材料与产品技术研究与开发",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "“以竹代塑”技术可以更好地发挥竹子在减少塑料污染、代替塑料产品方面的作用，为高能耗、难降解的塑料制品提供低碳、环保、循环的解决方案，潜在市场空间超千亿元",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "b83df6660c104702955365546d738648",
        "techDesc": "“以竹代塑”是中国政府同国际竹藤组织共同发起的倡议。竹子作为速生、可降解的生物质材料，是塑料的重要替代品，为减少塑料污染提供了有效解决途径。2022年6月24日，以“构建新时代全球发展伙伴关系，携手落实2030年可持续发展议程”为主题的全球发展高层对话会上，国际竹藤组织提出的“以竹代塑”倡议被列入全球发展高层对话会成果清单，并将由中国和国际竹藤组织共同发起，以减少塑料污染，应对气候变化，助力全球可持续发展。竹代塑产品包括餐盘、刀叉、筷子、勺子、水杯等一次性餐具，也包括汽车内饰、电子产品外壳、体育器材到产品包装、防护用品、重组竹、竹集成材、竹编工艺品、竹纤维制品、竹碳制品等100多个系列上万个品种。",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "浙江峰晖竹木制品有限公司",
            "id": "instance_entity_company-0742d97ca968c94a77f14994ffb6b07b"
          },
          {
            "name": "中林鑫宙竹缠绕发展有限公司",
            "id": "instance_entity_company-68844b8c91377743d41e4780b55d5ac3"
          },
          {
            "name": "重庆瑞竹植物纤维制品有限公司",
            "id": "instance_entity_company-449acabb7825c75a03fe01e80031f17a"
          },
          {
            "name": "浙江森林生物科技有限公司",
            "id": "instance_entity_company-3e9d2096ed8da614c86fc425979de49b"
          },
          {
            "name": "安徽鸿叶集团有限公司",
            "id": "instance_entity_company-5f6b59deadbce00432df265e9979d8f7"
          },
          {
            "name": "益阳和祥竹业有限公司",
            "id": "instance_entity_company-ea2190b6d2625f502f5e61df748e21a9"
          }
        ],
        "sort": 4915
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "纺织用竹纤维技术创新",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "竹纤维面料具有柔滑软暖、抑菌抗菌、吸湿透气、绿色环保、抗紫外线、天然保健、舒适美观等特点，是一种真正意义上的天然环保型绿色纤维。竹纤维提取及面料技术专利几乎被我国独占，是我国引领性的先进技术。线下调研时安吉竹产业研究院院长熊义勤也提出该技术",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "bb8614a5f996e458accabb1b4f84129e",
        "techDesc": "竹纤维面料是指是竹子为原料经特殊工艺制成竹纤维，经纺织而成的新型面料。具有：柔滑软暖、抑菌抗菌、吸湿透气、绿色环保、抗紫外线、天然保健、舒适美观等特点。专家指出，竹纤维是一种真正意义上的天然环保型绿色纤维。竹纤维是利用专利技术生产的，以竹子为原料，经特殊的高科技工艺处理，把竹子中的纤维素提取出来，再经制胶、纺丝等工序制造而生成的再生纤维素纤维。竹纤维面料包含竹纤维的多种天然性质，针织上的应用也相当广泛，毛巾，浴袍，贴身衣物，T恤等一系列的产品。薄型的有汗布、网眼等，厚实的有绒布、毛巾布、棉毛、华夫格等等。",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "安徽依采妮纤维材料科技有限公司",
            "id": "instance_entity_company-2a39c2389c12da5f3fc55f6ce0b8e3eb"
          },
          {
            "name": "浙江建中竹业科技有限公司",
            "id": "instance_entity_company-34c4aa5dd977a078df85872002c255a3"
          },
          {
            "name": "上海水星家用纺织品股份有限公司",
            "id": "instance_entity_company-70ccf4d88f0583960c5cf6de7f1bdfc6"
          },
          {
            "name": "常熟市梅李镇宾理服饰制衣厂",
            "id": null
          },
          {
            "name": "广东娜菲实业股份有限公司",
            "id": "instance_entity_company-b88f8d46eef4b3ca1d10d7d31d31b4ca"
          },
          {
            "name": "上海仪基堂实业有限公司",
            "id": null
          },
          {
            "name": "湖南欧林雅服饰有限责任公司",
            "id": "instance_entity_company-aa2b6aee3dfbacf838b120c99d0c3840"
          },
          {
            "name": "河北吉藁化纤有限责任公司",
            "id": "instance_entity_company-862b3c8ff764312cf5fe4fe6d42d30f2"
          },
          {
            "name": "安吉登冠竹木开发有限公司",
            "id": "instance_entity_company-2867ac766dc987d06941dcda68181f9d"
          },
          {
            "name": "安吉千竹坊生物科技有限公司",
            "id": "instance_entity_company-67f25f084cd96b964d9dce63f7b93d6a"
          },
          {
            "name": "浙江民心生态科技股份有限公司",
            "id": "instance_entity_company-02030ced052d3e07bd6ea8b90189503b"
          },
          {
            "name": "湖南竹莱纺服饰有限公司",
            "id": null
          },
          {
            "name": "青岛旭日纺织集团有限公司",
            "id": "instance_entity_company-7be6840a02cde643ed444211160e067b"
          },
          {
            "name": "山东竹之锦家纺科技有限公司",
            "id": null
          },
          {
            "name": "江西竹山蓝天竹纤维有限公司",
            "id": "instance_entity_company-db39987b274d26ab2c801c6ae31e561c"
          },
          {
            "name": "长沙木矛木心",
            "id": null
          },
          {
            "name": "绍兴县兰诺纺织品有限公司",
            "id": null
          }
        ],
        "sort": 4921
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "竹子优良品种选育和定向培育技术研究",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "充分发挥我国竹种质资源优势，开展竹种质资源重要性状鉴定、评价和重要功能基因挖掘。定向育种，定向培育材用竹、笋用竹、纸浆用竹、纤维用竹等竹类良种，从根本上保障竹资源高质量培育、竹产业基础的稳固与安全具有战略价值。也是国内主要竹类科研机构和专家的重点研究领域",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "150c7f6f99c18737c4304fdd9cdae9a4",
        "techDesc": "主要开展竹类良种选育及培育、优良竹种的丰产栽培技术研究、优良竹种种苗快繁技术研究及竹类新品种引种试验，在竹子种质创新、优良品种繁育进行技术攻关。建立了竹子转基因体系，研究竹子开花机理、杂交育种，形成毛竹春-冬-鞭笋定向培育技术、毛竹笋材两用林培育技术、毛竹材用林定向培育技术及其他特定功能的竹优良品种定性培育技术。",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "南京林业大学",
            "id": null
          },
          {
            "name": "浙江农林大学竹子研究院",
            "id": null
          },
          {
            "name": "浙江省林业科学研究院",
            "id": null
          },
          {
            "name": "江西省竹子种质资源与利用重点实验室",
            "id": null
          },
          {
            "name": "南京林业大学竹类研究所",
            "id": null
          },
          {
            "name": "中国林科院",
            "id": null
          },
          {
            "name": "国际竹藤中心竹藤资源基因科学与基因产业化研究所",
            "id": null
          },
          {
            "name": "湖南省林业科学院竹类研究所",
            "id": null
          }
        ],
        "sort": 4938
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "竹缠绕复合材料技术产业化与新产品开发研究",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "专利几乎被我国独占，是中国首创的技术，领先全球；文献研究中的热点领域；国家及各地规划均提出要重点发展该技术",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "3d1f621e2ddb0d278c226b29178ad132",
        "techDesc": "竹缠绕复合材料是指以竹子为基材，以树脂为胶黏剂，采用缠绕工艺加工成型的新型生物基材料。2017年，国家林业局在鑫宙公司设立竹缠绕工程技术研究中心，竹缠绕复合材料应用与推广工程被国家发改委、国家林业局等11部委列入《林业产业发展十三五规划》重点领域，计划五年内在全国布局1000万吨的竹缠绕复合材料生产基地。竹缠绕复合材料的关键技术：1、适用性竹种筛选、竹材分类及其材性评价；2、连续缠绕用薄竹篾自动化加工技术；3、竹缠绕复合材料材性的测试、工艺调整；4、自动化、连续化、工业化成套生产设备的开发与工艺跟进；5、竹缠绕复合材料产品信息化管理技术；6、建立竹缠绕复合材料产品生命周期模型。竹缠绕复合材料可广泛替代钢材、水泥、玻璃钢、塑料等不可回收的高污染和高能耗原材料，还具有低碳、节能、减排等优势，可应用于交通、市政、水利、建筑及军工等多个领域。目前，在研发的竹缠绕复合材料产品中，竹缠绕复合管、竹缠绕管廊、竹缠绕整体组合式房屋已进入推广应用和产业化阶段。",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "国家林业和草原局竹缠绕复合材料工程技术研究中心",
            "id": null
          },
          {
            "name": "浙江鑫宙竹基复核材料科技有限公司",
            "id": null
          },
          {
            "name": "国际竹藤中心",
            "id": null
          },
          {
            "name": "南京林业大学",
            "id": null
          },
          {
            "name": "湖南九通竹基复合材料制造有限公司",
            "id": "instance_entity_company-24b2c62cdae5020e6b2a26a697511b5d"
          },
          {
            "name": "中林鑫宙竹缠绕发展有限公司",
            "id": "instance_entity_company-68844b8c91377743d41e4780b55d5ac3"
          }
        ],
        "sort": 4946
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "竹质提取物及化学品加工综合利用技术研究",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "全球研究热点，发明专利前10名中9个国外企业，国外产业化研究优势显著；IPC分类广泛应用于食品、保健、化妆品、药品、饲料添加剂等领域",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "b8bff19d6e748d939522f89d87768c46",
        "techDesc": "竹叶提取物是一种植物类黄酮制剂,其有效成分包括黄酮生物活性多糖、特种氨基酸芳香成分等,但黄酮类是其最主要的成分,也是其最主要的功能因子,故一般也将竹叶提取物称为竹叶黄酮。近年来,竹叶提取物更以其丰富的原料来源、明确的生理功能、良好的安全性和清新甜香的竹子风味,在天然功能性食品和医药保健品领域崭露头角。竹叶提取物及产品可应用于医药中间体、天然食品添加剂等重要领域,具有良好的经济效益。我国竹叶资源丰富,所以，竹叶提取物在国内市场的发展非常迅速。目前,竹叶提取物及其系列产品均已上市,并有小批量出口至日、韩、美、加等国家,市场反应良好市场对竹叶提取物的食用安全性工艺稳定性和其风味的独特给予了高度评价，认为是一种优良的保健食品功能因子,可以广泛应用于保健营养品。竹叶提取物的产业应用价值还体现在可作为天然食品抗氧化剂、生物杀虫剂;作为天然功能性添加剂应用于饮料的生产。市场上已经有利用竹叶提取物生产的保健啤酒一一竹啤。此外,竹叶提取物还可用于药品、化妆品、香料等领域。",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "国际竹藤中心化学所",
            "id": null
          },
          {
            "name": "国家林业和草原局丛生竹工程技术研究中心",
            "id": null
          },
          {
            "name": "浙江农林大学",
            "id": null
          },
          {
            "name": "浙江佶竹生物科技有限公司",
            "id": "instance_entity_company-c10d7172335ac30f0c2946d7dc9eb169"
          },
          {
            "name": "浙江竹孝宝生物科技股份有限公司",
            "id": "instance_entity_company-e672116f73cbf5a6e3f3a5bdd16a7bef"
          },
          {
            "name": "江苏多阳生物工程科技有限公司",
            "id": "instance_entity_company-dccc053bd16809b110895ecf8e82f7e3"
          },
          {
            "name": "陕西斯诺特生物技术有限公司",
            "id": "instance_entity_company-9547df3e0f339986d28862543b9148ca"
          },
          {
            "name": "成都万象宏润生物科技有限公司",
            "id": "instance_entity_company-dce6fdb091f66d654ff3d474597eb5b7"
          },
          {
            "name": "合肥盛润生物制品有限公司",
            "id": "instance_entity_company-92a8be50f040abc90cba5b6098604030"
          },
          {
            "name": "山西玉宁生物科技有限公司",
            "id": "instance_entity_company-60a5e73483d0d61bbd1273880381bfb4"
          },
          {
            "name": "淮南市科迪化工科技有限公司",
            "id": "instance_entity_company-801a5a38c968dfd00adc270dfde78cf2"
          },
          {
            "name": "南京普怡生物科技有限公司",
            "id": "instance_entity_company-66572fe877d92056ecaa65d264eac18e"
          },
          {
            "name": "浙江圣氏生物科技有限公司",
            "id": "instance_entity_company-6dce2e2103447606b3a651baa76776ef"
          }
        ],
        "sort": 4952
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "可降解竹塑复合材料技术",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "可降解竹塑复合材料是当前文献研究和专利申请的热点，中国专利申请独步全球，中南林业科技大学吴义强院士在课题《竹纤维/可生物降解塑料绿色复合材料制备的关键问题》也提及了关键技术",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "8b6326f88a89837765118ba083dc3090",
        "techDesc": "以竹纤维为增强材料，生物可降解塑料为基体材料经过特定工艺加工而成的复合材料称之为生物可降解竹纤维增强塑料复合材料。它具有密度小、防虫蛀、吸潮、隔音、减震、降噪、耐冲击性高、力学性能好以及可生物降解等众多优点，是一种新型的优良环保材料，已经广泛应用于国防、建筑、交通运输、工程和日常生活等领域。因此，生物可降解竹纤维增强塑料复合材料的研究越来越引人关注。目前，生物可降解竹纤维增强塑料复合材料的成型工艺主要包括模压成型、注射成型、挤出成型以及层压成型工艺等，可以根据产品的结构与使用要求及生产数量合理灵活地选择成型工艺。",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "国际竹藤中心",
            "id": null
          },
          {
            "name": "赣南师范大学竹基新材料与物质转化工程研究中心",
            "id": null
          },
          {
            "name": "浙江峰晖竹木制品有限公司",
            "id": "instance_entity_company-0742d97ca968c94a77f14994ffb6b07b"
          },
          {
            "name": "浙江森林生物科技有限公司",
            "id": "instance_entity_company-3e9d2096ed8da614c86fc425979de49b"
          },
          {
            "name": "浙江味老大工贸有限公司",
            "id": "instance_entity_company-977823d80b356c971567ebb02c361b17"
          },
          {
            "name": "宁波士林工艺品有限公司",
            "id": "instance_entity_company-fdb4d9b04fd97c33930567b54acb1188"
          }
        ],
        "sort": 4965
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "高效节能竹材采伐机具",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "竹林采伐一直是制约竹产业发展的瓶颈问题。为解决竹材收割费时、得竹率低和农村劳动力短缺等难题，加快提升竹林生产机械化水平，可大大提高筏竹效率，降低劳动成本，实现竹材高效、低成本开采。《全国竹产业发展规划（2021—2030年）》要求到 2030 年，竹材采伐运输机械化率达 40%~50%",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "aacd11a52ea9b594a344d8cff7f8d62b",
        "techDesc": "长期以来，竹林生产活动几乎全靠人工，竹子间伐、拖拽、挖蔸、去枝等工序都颇费劳力。随着劳动力成本不断升高，导致竹林抚育、采伐和加工作业的人工成本占比增大，严重制约了竹林、竹材加工生产链的发展。必须实现机械化的生产，才能降低成本和提高效率。",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "浙江省林业科学研究院竹类(智能装备)",
            "id": null
          },
          {
            "name": "龙泉市大展竹科技有限责任公司",
            "id": "instance_entity_company-db11e7e0a8c11f531ec350ad693b738a"
          },
          {
            "name": "石家庄灿高高频机械有限公司",
            "id": "instance_entity_company-43f1ee216c274486db65582ede928cf2"
          },
          {
            "name": "济南全劲试验机有限公司",
            "id": null
          },
          {
            "name": "安吉前程机械有限公司",
            "id": null
          },
          {
            "name": "安吉吉泰机械有限公司",
            "id": "instance_entity_company-501340ca87fed7650c167690e6295bf7"
          },
          {
            "name": "国家林业和草原局北京林业机械研究所，湖南省林业科学院",
            "id": null
          },
          {
            "name": "南京林业大学",
            "id": null
          },
          {
            "name": "东莞市嘉航实业有限公司",
            "id": "instance_entity_company-baa5a55b21641549501edb7ea8d730f0"
          },
          {
            "name": "中国福马机械集团有限公司",
            "id": "instance_entity_company-f84a364b6074591746a51d9aa986f8fe"
          }
        ],
        "sort": 4971
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "竹炭及其衍生物绿色制造技术研究",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "竹碳及衍生物绿色制造技术是当前对竹产业加工中剩余的废料、下脚料绿色处理、高价值利用的关键技术，也是当前国内外研究的热点，对竹炭的微观结构、结晶状态，对其吸附有害气体、水质净化、抗电磁性干扰及对人体健康影响进行研究。近5年来，中国竹炭方面的发明专利占全球的75%，论文占54%，遥遥领先。国内开发了以竹炭竹醋液为原料吸附净化系列、纳米改性竹炭系列、竹炭纤维系列、竹炭基复合系列、竹炭工艺品系列、竹炭护肤洗涤用品系列、竹炭保健系列300多个品种的产品，是竹子高附加值加工的重要领域。",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "6567bb0e08499b3a54276308e2d57f38",
        "techDesc": "竹炭是以三年生以上高山毛竹为原料，经近千度高温烧制而成的一种炭。竹炭具有疏松多孔的结构，其分子细密多孔，质地坚硬。有很强的吸附能力，能净化空气、消除异味、吸湿防霉、抑菌驱虫。与人体接触能去湿吸汗，促进人体血液循环和新陈代谢，缓解疲劳。经科学提炼加工后，已广泛应用于日常生活中。由于炭质本身有着无数的孔隙，这种炭质气孔能有效地吸附空气中一部分浮游物质，对硫化物、氢化物、甲醇、苯、酚等有害化学物质起到吸附、分解异味和消臭作用。重点发展方向是开发竹质活性炭、竹炭洗护产品、竹炭空气净化产品、冰箱除味竹炭、竹炭日化产品、竹炭装饰板、竹炭水质净化产品、竹炭环保布、纳米竹炭粉和竹炭纤维、竹炭工艺品，以及洗涤洁肤及竹醋液、竹沥液（鲜竹沥）等多种产品。\n",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "南京林业大学",
            "id": null
          },
          {
            "name": "浙江农林大学",
            "id": null
          },
          {
            "name": "浙江省林业科学研究院",
            "id": null
          },
          {
            "name": "江西农业大学",
            "id": null
          },
          {
            "name": "南京林业大学",
            "id": null
          },
          {
            "name": "中国林业科学研究院",
            "id": null
          },
          {
            "name": "国际竹藤中心",
            "id": null
          },
          {
            "name": "湖南省林业科学院",
            "id": null
          }
        ],
        "sort": 4981
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "竹叶黄酮提取技术",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "竹叶黄酮一种高效的生物抗氧化剂，对大脑和心脏功能不全，血管硬化等疾病有重要作用，属生物医学前沿研究领域，是中国首创的、具有中国本土资源特色和自主知识产权的植物类黄酮制剂，专利上独步全球。线下调研时安吉竹产业研究院院长熊义勤也提出该技术",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "7d2406b7f74b020222d44c03eaa3dc37",
        "techDesc": "竹叶黄酮是一种天然有机混合物。主要由荭草苷（Orientin）、异荭草苷（Homoorientin）、牡荆苷（Vitexin）和异牡荆苷（Isovitexin）组成。具有较大的极性和亲水性，易溶于热水、甲醇、乙醇、丙酮等溶剂。其分子中都具有酚羟基和糖苷链，生成氢键的能力较强，有利于弱极性和极性树脂吸附，可以使用大孔树脂等有机高分子聚合物吸附剂来进行纯化。又名竹叶抗氧化剂，英文名称antioxidantofbambooleaves,简称AOB，是从竹叶中提取出来的具有生理活性的生物黄酮，它是一种高效的生物抗氧化剂，是人体必需的营养素。当人体缺乏时，容易导致大脑和心脏功能不全，血管硬化等疾病的产生。国际学术界从20世纪90年代起关注碳苷黄酮，此领域属最新的研究前沿。竹叶中的碳苷黄酮主要有荭草苷、异荭草苷、牡荆苷和异牡荆苷4种。竹叶黄酮是中国首创的、具有中国本土资源特色和自主知识产权的植物类黄酮制剂，用专利技术生产的竹叶黄酮是一类多组分协同的生物抗氧化剂，其成分除了黄酮类化合物以外，还有酚酸、蒽醌类化合物、芳香成分和锰、锌、锡等微量元素，它们共同构成了竹叶黄酮广泛的生理和药理活性的基础。",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "国际竹藤中心化学所",
            "id": null
          },
          {
            "name": "国家林业和草原局丛生竹工程技术研究中心",
            "id": null
          },
          {
            "name": "浙江农林大学",
            "id": null
          },
          {
            "name": "浙江佶竹生物科技有限公司",
            "id": "instance_entity_company-c10d7172335ac30f0c2946d7dc9eb169"
          },
          {
            "name": "浙江竹孝宝生物科技股份有限公司",
            "id": "instance_entity_company-e672116f73cbf5a6e3f3a5bdd16a7bef"
          },
          {
            "name": "江苏多阳生物工程科技有限公司",
            "id": "instance_entity_company-dccc053bd16809b110895ecf8e82f7e3"
          },
          {
            "name": "陕西斯诺特生物技术有限公司",
            "id": "instance_entity_company-9547df3e0f339986d28862543b9148ca"
          },
          {
            "name": "成都万象宏润生物科技有限公司",
            "id": "instance_entity_company-dce6fdb091f66d654ff3d474597eb5b7"
          },
          {
            "name": "合肥盛润生物制品有限公司",
            "id": "instance_entity_company-92a8be50f040abc90cba5b6098604030"
          },
          {
            "name": "山西玉宁生物科技有限公司",
            "id": "instance_entity_company-60a5e73483d0d61bbd1273880381bfb4"
          },
          {
            "name": "淮南市科迪化工科技有限公司",
            "id": "instance_entity_company-801a5a38c968dfd00adc270dfde78cf2"
          },
          {
            "name": "南京普怡生物科技有限公司",
            "id": "instance_entity_company-66572fe877d92056ecaa65d264eac18e"
          },
          {
            "name": "浙江圣氏生物科技有限公司",
            "id": "instance_entity_company-6dce2e2103447606b3a651baa76776ef"
          }
        ],
        "sort": 4989
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "竹纤维及其制品高附加值利用技术研究",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "竹纤维作为一种新型天然纤维，是当前竹材加工、新材料、纺织领域研究的热点之一，竹纤维的新产品收到社会的广泛关注，但是目前竹纤维制备技术工业化使用程度不高，寻求更高的生产工艺技术装备，实现高附加值的竹纤维产品生产，是当前迫切需要攻克的生产技术难题",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "0c424c472efc9d8b8479005082187e90",
        "techDesc": "天然竹纤维是以竹子原料经机械、物理方法提取而成。在加工过程中，不破坏竹材的纤维束结构，只去除纤维束外的植物组织。竹子单纤维长度较小，一般在2mm右川，多用于造纸制浆。通常，竹纤维以纤维束形式应门其纵向由多根单纤维粘结组成，形状与大麻、黄麻、工麻等相近。竹纤维可分为竹原纤维、竹浆纤维和竹炭纤维。竹纤维的应用领域比较广，近年来国内外相关研究逐年增加，尤其在非织造材料、复合材料中的应用研究日益广泛，可纺竹纤维是目前研究的热点和难点。2003年以来，国内一些院校与日本多家公司合作开发出车用天然竹纤维非织造材料和复合材料，试生产的产品有:轿车的门内板(行李厢、顶棚、座椅背板，以及卡/客车的车厢内衬板、门板、顶棚、座椅背板等。2004年还开发出天然竹纤维隔热/音和阻尼材料，在2005年日本爱知博览会上受到极大关注。将来，用天然竹纤维的热塑性或热固性模压件(如车门板等)可望成为轿车的标准配置，作为一种新的加工技术一一天然竹纤维+聚丙烯注射模压技术将成为今后的发展趋势。用竹纤维作增强相的复合材料是目前材料领域研究的热点问题之一。天然竹纤维横截面的高度“中空”的结构决定了其具有优良的吸水和导湿性除臭等成分，特别适合用于纺织用品的开发。另外，竹纤维可以用来生产无纺布、地毯等产品。基于竹结构的柔性电极材料、饲料、医疗辅料也是热点研究方向。",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "国家林业和草原局竹子研究开发中心",
            "id": null
          },
          {
            "name": "浙江鑫宙竹基复合材料科技有限公司",
            "id": "instance_entity_company-e711f0b86f949b8b505576c18b139327"
          },
          {
            "name": "国际竹藤中心",
            "id": null
          },
          {
            "name": "南京林业大学",
            "id": null
          },
          {
            "name": "广东裕丰竹纤维有限公司",
            "id": "instance_entity_company-6abe9f94b2157799447397b69b1292ec"
          },
          {
            "name": "湖南九通竹基复合材料制造有限公司",
            "id": "instance_entity_company-24b2c62cdae5020e6b2a26a697511b5d"
          },
          {
            "name": "中林鑫宙竹缠绕发展有限公司",
            "id": "instance_entity_company-68844b8c91377743d41e4780b55d5ac3"
          }
        ],
        "sort": 5002
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "竹缠绕吸管技术",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "低碳环保使不可降解一次性塑料吸管成为被限制产品。吸管在日常生活中高频使用，年消耗量近460亿根。替代品纸吸管和PLA生物可降解吸管存在性能缺陷和价高昂贵的问题，普通小口径竹子加工的吸管和实心竹钻孔吸管存在轴心偏差和壁厚较大问题。缠绕式竹吸管采用刨切得到0.25mm超薄竹片双层连续缠绕制成，实现了更好的产品一致性和使用体验，可工业化标准生产，具有功能性和成本优势。该技术在专利申请上我国全球领先，是我国独创的领先型技术。",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "8ae5f0e16ec766ea263c24a0024bf20c",
        "techDesc": "竹吸管采用的几项重点技术——圆竹展平、刨切薄片、纵向接长、缠绕成型。在传统的竹材加工中，通常只能将圆筒形竹材纵向剖成长条形材料加以利用，业内称之为竹坯。后来发展出刻痕式展开技术，即通过在竹筒内部横向刻划，释放竹材内部的应力后实现横向展平。龙竹科技研发出的圆竹展开技术为无刻痕式。即通过自主研发的碳化工艺将原竹软化后，再实施展平。无刻痕展开技术不仅保持了竹材原有的柔韧性和使用寿命，也显著提高了合格品率，降低了材料成本。刨切薄片技术同样属业内首创。此前想获取竹材的薄片，只能横向刨切，薄片的大小取决于竹子展开后的横截面大小。龙竹科技的纵向刨切，则是在展开的竹材上从头至尾揭起一层又一层，可以获取整根竹子长度的薄片。由于纵切并未破坏竹纤维，其柔韧性很强，适用范围也更广。竹吸管即是将这种薄片纵向缠绕加工后所得。专家组对竹吸管进行试验后表明，其耐温性是纸吸管的1.5倍，受温达80度以上；可全部降解；可重复使用。",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "国际竹藤中心",
            "id": null
          },
          {
            "name": "赣南师范大学",
            "id": null
          },
          {
            "name": "浙江峰晖竹木制品有限公司",
            "id": "instance_entity_company-0742d97ca968c94a77f14994ffb6b07b"
          },
          {
            "name": "重庆瑞竹植物纤维制品有限公司",
            "id": "instance_entity_company-449acabb7825c75a03fe01e80031f17a"
          },
          {
            "name": "浙江森林生物科技有限公司",
            "id": "instance_entity_company-3e9d2096ed8da614c86fc425979de49b"
          },
          {
            "name": "安徽鸿叶集团有限公司",
            "id": "instance_entity_company-5f6b59deadbce00432df265e9979d8f7"
          },
          {
            "name": "益阳和祥竹业有限公司",
            "id": "instance_entity_company-ea2190b6d2625f502f5e61df748e21a9"
          }
        ],
        "sort": 5009
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "笋竹精深加工技术",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "目前国内多数竹材加工产品单一,生产规模化程度低、精加工高附加产品少的情况，“以竹代木”、“以竹代塑”、“以竹代刚”、竹纤维、竹吸管、竹活性提取物等新产品是笋竹精深加工研究的热点方向，相应的加工机械、设备也成为了研究热点。该技术也是《全国竹产业发展规划》提及重点突破的技术。",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "cf6f4a420354a8a8b5177aba611dfc6b",
        "techDesc": "“十三五”期间，为针对竹笋加工产业中竹笋利用率低产品单一，生产规模化程度低、精加工高附加产品少、产品食用不安全等问题，通过对竹笋加工产业链中竹笋速冻保鲜技术、竹笋高pH值保鲜技术、竹笋粉制造技术、竹笋饲料或肥料利用技术、竹笋营养学和安全生产技术等关键技术有机集成和创新研究，建立竹笋营养学和安全性评价体系，构建一套全笋分级利用规模化环保无公害生产技术体系，建立速冻笋、高pH值保鲜笋、竹笋粉和竹笋饲料或肥料规模生产线",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "国家林业和草原局竹子研究开发中心",
            "id": null
          },
          {
            "name": "云南省农业科学院农产品加工研究所",
            "id": null
          },
          {
            "name": "浙江省林业科学研究院",
            "id": null
          },
          {
            "name": "湖南省林业科学院",
            "id": null
          },
          {
            "name": "江西广雅食品有限公司",
            "id": "instance_entity_company-deb359cc2ac23067f820e9e1000d521f"
          },
          {
            "name": "乐山弘翰供应链管理服务有限公司",
            "id": null
          }
        ],
        "sort": 5016
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "刨切微薄竹生产技术与应用",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "该技术以竹材为原料，在国内外首次成功地研制了刨切微薄竹及系列产品，开创了竹材加工利用新领域，是竹材精深加工的新技术。该发明的实施，使我国成为世界上唯一拥有刨切微薄竹生产技术自主知识产权和产品的国家。",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "8356fa12485b5112970bcc5c6df00c3d",
        "techDesc": "该技术以竹材为原料，在国内外首次成功地研制了刨切微薄竹及系列产品，开创了竹材加工利用新领域，是竹材精深加工的新技术。该发明的实施，使我国成为世界上唯一拥有刨切微薄竹生产技术自主知识产权和产品的国家，其制造技术完全不同于木材刨切，具有自己独特的工艺技术。该发明处于国内外同类技术领先水平。主要技术经济指标：（1）刨切微薄竹厚0.3－0.8mm，幅面可根据需要确定；（2）甲醛释放量≤0.7mg·L－1，达到E1级国家标准，可直接使用；（3）抗拉强度：顺纹≥56.0MPa；横纹≧2.2MPa；（4）0.5mm厚的刨切微薄竹。产品广泛用于人造板、家具、装饰装修等领域，如建造了西班牙马德里机场的天花板、意大利王储休息室的护墙板等国际上知名的大型工程。",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "浙江农林大学",
            "id": null
          },
          {
            "name": "浙江大庄实业集团有限公司",
            "id": "instance_entity_company-8734d55dd6536fb01112d5b54824dec9"
          },
          {
            "name": "杭州强生圣威装饰材料有限公司",
            "id": null
          },
          {
            "name": "德华建材(苏州)有限公司",
            "id": null
          }
        ],
        "sort": 5022
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "笋竹源活性物质测试、筛选、提取精制改性技术",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "《全国竹产业发展规划》中指出要突破该技术，加大笋竹源药物研制.竹沥,竹茹,竹黄等是中国传统的中 药或中药材研究，建立笋竹源药物、中间体测试筛选机制。该技术产物广泛应用于医药、食品、保健、化妆品、饲料添加剂等领域，是国内外研究的热点。",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "74ac3a93e7200a103e9866ba84f94a79",
        "techDesc": "竹沥、竹茹、竹黄等是中国传统的中药或中药材。竹叶提取物不仅添加在食品中可以抗食品氧化，从而起到保鲜、护色、防腐的作用，摄入到人体中，也能帮助人体抗氧化，特别是其中的竹叶黄酮成分能通过抗氧化预防因氧化而引起的心血管疾病、炎症、衰老等，因此有很好的医疗保健价值。竹茹提取物是通过专利技术从竹子的外表皮中得到的淡绿色粉末，以五环三萜类化合物为主，其三萜总皂苷元的含量在40％～70％之间，具有优良的抗肿瘤、抗疲劳和降压功效，可作为医药中间体和保健品原料。竹笋提取物主要是采用组合式膜分离技术从竹笋中得到的植物甾醇、生物活性多糖和氨基酸浓缩液等。竹笋甾醇可以作为激素类药物的前体，也可以作为调节胆固醇的功能成分；竹笋多糖具有优良的抗肿瘤、增强免疫和促进肠道益生菌生长的作用，可作为保健食品的功能因子和普通食品的营养强化成分。竹笋氨基酸浓缩液可作为天然增鲜剂用于调味品等领域。",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "国际竹藤中心",
            "id": null
          },
          {
            "name": "国家林业和草原局",
            "id": null
          },
          {
            "name": "浙江农林大学",
            "id": null
          },
          {
            "name": "浙江佶竹生物科技有限公司",
            "id": "instance_entity_company-c10d7172335ac30f0c2946d7dc9eb169"
          },
          {
            "name": "浙江竹孝宝生物科技股份有限公司",
            "id": "instance_entity_company-e672116f73cbf5a6e3f3a5bdd16a7bef"
          },
          {
            "name": "江苏多阳生物工程科技有限公司",
            "id": "instance_entity_company-dccc053bd16809b110895ecf8e82f7e3"
          },
          {
            "name": "陕西斯诺特生物技术有限公司",
            "id": "instance_entity_company-9547df3e0f339986d28862543b9148ca"
          },
          {
            "name": "成都万象宏润生物科技有限公司",
            "id": "instance_entity_company-dce6fdb091f66d654ff3d474597eb5b7"
          },
          {
            "name": "合肥盛润生物制品有限公司",
            "id": "instance_entity_company-92a8be50f040abc90cba5b6098604030"
          },
          {
            "name": "山西玉宁生物科技有限公司",
            "id": "instance_entity_company-60a5e73483d0d61bbd1273880381bfb4"
          },
          {
            "name": "淮南市科迪化工科技有限公司",
            "id": "instance_entity_company-801a5a38c968dfd00adc270dfde78cf2"
          },
          {
            "name": "南京普怡生物科技有限公司",
            "id": "instance_entity_company-66572fe877d92056ecaa65d264eac18e"
          },
          {
            "name": "浙江圣氏生物科技有限公司",
            "id": "instance_entity_company-6dce2e2103447606b3a651baa76776ef"
          }
        ],
        "sort": 5026
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "竹子功能基因组和生物技术研究",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "该技术是竹子育种研究的重点关键热点技术，是国内外优良竹子育种的前沿技术，也是《全国竹产业发展规划（2021—2030年）》及多个地方规划提及的技术。",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "c14da16411e5382b49937fc0ca654686",
        "techDesc": "竹类植物属于禾本科竹亚科，是非常重要的森林资源,在全球森林覆盖率日趋减少的情况下,竹子以其生长迅速、可再生性强的特点,在缓解木材供需矛盾、促进生态建设和环境保护方面发挥了重要作用，其开发利用在全球范围内引起了高度关注。因此,作为基础性研究竹类植物基因组学日趋受到重视。目前，竹类植物基因组学研究主要集中在分子标记的开发与应用,功能基因的分离与特征分析，基于高通量测序的EST、全长CDNA、基因组的分析与比较基因组学等方面。",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "南京林业大学",
            "id": null
          },
          {
            "name": "浙江农林大学",
            "id": null
          },
          {
            "name": "浙江省林业科学研究院",
            "id": null
          },
          {
            "name": "江西农业大学",
            "id": null
          },
          {
            "name": "南京林业大学",
            "id": null
          },
          {
            "name": "中国林科院",
            "id": null
          },
          {
            "name": "国际竹藤中心",
            "id": null
          },
          {
            "name": "湖南省林业科学院",
            "id": null
          }
        ],
        "sort": 5039
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "竹林生态与人类健康关系研究",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "研究表明，人类健康与森林的联系可能远远超出你的想象。WWF 2021年发布的《全球森林生命力展望》报告中强调，毁林和林地退化是人畜共患病的主要驱动因素。当森林处于健康状态时，它是抵御新冠病毒等疾病的缓冲带；但当森林饱受攻击时，它的保护系统会被削弱，最终导致疾病的蔓延。我市竹林占据了很大一部分，研究竹林生态与人类健康的关系，对我市人们的健康和防疫具有重要的意义。",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "f1cc6328324dccc7df0458c153673989",
        "techDesc": "森林不仅具有涵养水源、减少水土流失、吸收二氧化碳、释放氧气、滞尘等良好的生态功能，还因为富氧的环境、洁净的空气、较高的负离子含量、舒适的森林小气候、益身的植物精气等丰富的保健效益因子而对人体具有调养、减压等康体健身作用，是人们游憩、休闲、保健、疗养的优良场所。竹子是生长迅速的高吸碳植物，活体竹叶会释放萜烯类及醇类化合物等游憩成分，具有康体保健的效果。夏季毛竹林内温湿度条件、负离子浓度及挥发性有机物使竹类公园更适合开发康养资源，开展竹林生态与人类健康关系研究。竹林生态与人类健康关系研究对当下竹类公园发展健康产业，充分利用竹林资源，发挥其康养功能，并建设“森林浴场”等竹林消夏场所，满足人们日益增长的康养需求具有重要意义。",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "南京林业大学",
            "id": null
          },
          {
            "name": "浙江农林大学",
            "id": null
          },
          {
            "name": "浙江省林业科学研究院",
            "id": null
          },
          {
            "name": "江西农业大学",
            "id": null
          },
          {
            "name": "南京林业大学",
            "id": null
          },
          {
            "name": "中国林业科学研究院",
            "id": null
          },
          {
            "name": "国际竹藤中心",
            "id": null
          },
          {
            "name": "湖南省林业科学院",
            "id": null
          }
        ],
        "sort": 5047
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "竹产品固碳减碳以及全生命周期评价研究",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "经研究表明，竹林固碳能力远超普通林木,是杉木的1.46倍、热带雨林的1.33倍，竹产品的整个生命周期形成固碳储碳与节能减碳的叠加效应，是名副其实的绿色低碳产品。竹固碳减碳以及全生命周期评价研究，可扩大竹木产品在市场上的低碳环保影响力；通过对生命周期碳排放的有效管理，可优化生产环节的低碳减排措施，增强竹木加工企业的核心环保竞争力；在促进绿色低碳发展、应对“碳关税”等绿色贸易规则方面具有显著优势和应用潜力。",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "be49450e0df82b585b87eede848abe14",
        "techDesc": "在“双碳”背景下，竹子资源的高效利用在维护自然生态平衡方面发挥着重要作用。竹材是高效廉价的碳封存体，相比钢材、水泥、陶瓷、塑料、玻璃等材料，竹材制品的加工过程排放量最低，对环境的负面影响最小。在“双碳”战略指引下，竹材工业迎来重要发展机遇期，加强木竹建材等低碳建材产品研发应用，有利于加快推动竹材工业领域碳达峰行动。通过开展基于全生命周期的木/竹碳储量与碳足迹研究，对更加全面了解森林碳汇在区域碳中和中的贡献，评估产品的替代减排效应、助力竹材加工领域实现碳中和具有重要意义，同时可为企业进行清洁生产工艺改进和绿色设计提供规范化指导，为产业结构调整和低碳绿色管理提供数据支撑和决策依据。",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "南京林业大学",
            "id": null
          },
          {
            "name": "浙江农林大学",
            "id": null
          },
          {
            "name": "浙江省林业科学研究院",
            "id": null
          },
          {
            "name": "江西农业大学",
            "id": null
          },
          {
            "name": "南京林业大学",
            "id": null
          },
          {
            "name": "中国林业科学研究院",
            "id": null
          },
          {
            "name": "国际竹藤中心",
            "id": null
          },
          {
            "name": "湖南省林业科学院",
            "id": null
          }
        ],
        "sort": 5055
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "国内外竹产品生产与消费市场数据信息分析研究",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "国内外竹产品生产与消费市场数据信息分析研究这是竹产业发展的重要情报技术，对竹企业新产品的开发、竞争决策具有关键的作用，目前缺乏专业机构系统性、持续性提供这方面的日常情报信息",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "64003589a717f3ed0c8de194c02b01f4",
        "techDesc": "竹产品主要是指以竹子为加工原料制造的产品，多为日用品，包括竹筷、竹扫帚、竹席、竹床、竹凳、竹椅、竹躺椅、凉席、茶杯垫等。其中竹地板、竹家具、竹炭产品以及竹雕等价值较高民间工艺品是较为流行的产品。通过国内外竹产品生产与消费市场数据信息分析研究，及时掌握竹资源全球分布和特点，了解市场需求趋势和消费者偏好，对厂商布局产能、开发新产品、拓展新市场具有重要意义。",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "国际竹藤中心",
            "id": null
          }
        ],
        "sort": 5063
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "高防潮型竹刨花板制备关键技术与产业化应用",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "南平市位于福建省，竹资源丰富，尤其是毛竹。高防潮型竹刨花板能充分利用这些资源，提高利用率，并符合全球环保和可持续发展趋势，市场需求广泛。制备技术涉及材料科学、机械工程、化学工程等多个领域，对技术创新要求高。攻克该技术难题，有助于提升南平市竹产业技术水平，推动产业向高附加值、高技术含量方向升级。传统竹材产品易受潮变形、发霉，而高防潮型竹刨花板能稳定性能，延长使用寿命，带动上下游产业链发展，创造经济效益和就业机会，同时减少对木材资源的依赖，保护森林，实现绿色发展。总之，高防潮型竹刨花板制备技术和产业化应用对南平市竹产业链升级、经济效益提升和环保有重要意义。",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "200d42f72e506ae1e2b1684816d143da",
        "techDesc": "本研究围绕高防潮型竹刨花板的开发，通过石蜡防水剂的改性优化、制备工艺改进及其作用机理的深入研究，提升板材的防潮性能和长期稳定性。研究内容包括石蜡的改性优化、微胶囊化技术的开发、高防潮型竹刨花板的制备工艺及性能评估，及石蜡在竹刨花板中防水机理的探索。",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "中国林业科学研究院木材工业研究所",
            "id": null
          }
        ],
        "sort": 5073
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "重组竹多程长效防霉关键技术研发",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "南平市拥有丰富的竹资源，是“中国笋都竹乡”，竹材加工产业基础较好。因此，开发竹材深加工产品，尤其是具有特殊功能的产品，能够更好地发挥当地资源优势。随着人们环保意识的增强和可持续发展的需求，对竹材产品的需求日益增长。然而，竹材容易受潮、发霉，限制了其应用范围。重组竹多程长效防霉技术能够提高竹材产品的使用寿命和品质，满足市场需求。南平市竹产业链正处于转型升级的关键时期。通过研发防霉技术，可以提升竹材产品的附加值，推动产业向高附加值领域转型，促进竹产业可持续发展。该技术涉及材料科学、化学、生物工程等多个领域，对于推动南平市竹产业链的技术创新具有重要意义。通过攻克防霉技术难题，可以提升南平市竹产业的整体技术水平。防霉技术的研发和应用将带动上下游产业链的协同发展，包括竹材种植、加工、销售、物流等环节。这将有助于优化产业链结构，提高整体竞争力。竹材是一种可再生的环保材料，防霉技术的应用可以减少竹材浪费，降低环境污染。这符合国家关于绿色发展和生态文明建设的战略要求。防霉技术的成功研发和应用将提高竹材产品的市场竞争力，增加企业收益，带动就业，对南平市经济发展具有积极作用。综上所述，重组竹多程长效防霉关键技术研发对于南平市竹产业链来说是一个具有重要战略意义的技术，需要重点攻关和推广。",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "80f04df193fe859e57a3fed7885c3142",
        "techDesc": "针对重组竹易发霉、长效防霉效果不理想的技术挑战，提出“前程”+“中程”+“后程”的多\n程协同防霉处理思路，研究重组竹高效前处理浸渍技术、多元防霉复合技术和长效防霉后处理\n技术，最终构建重组竹多程协同防霉体系，提高重组竹的长效防霉性能",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "中国林业科学研究院木材工业研究所",
            "id": null
          }
        ],
        "sort": 5074
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "高强高耐多功能网络构建技术及在工程竹应用研究",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "南平作为全国毛竹林面积最大的设区市，可通过‌高强高耐多功能网络构建技术‌实现竹资源向经济优势转化。该技术能提升竹材性能（强度、耐久性等），拓展建筑、家具等领域的应用，推动产业升级并提高附加值。同时，开发竹基复合材料可延长产业链，减少对钢材、塑料的依赖，契合全球可持续发展趋势，增强市场竞争力。此外，技术融合（智能制造、新材料等）将形成新产业生态，叠加国家政策支持，助力南平竹产业实现绿色升级与全球化布局。",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "7d7f75a1eeee12cd47f236aa004f9bd4",
        "techDesc": "（1）竹材组分定向拆分脱除技术；（2）高强高耐多功能网络构建技术；（3）工程竹加工单元结构设计与稳定性评价；（4）竹质工程材料高效成型技术；（5）工程竹标准化体系建设",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "中国林业科学研究院木材工业研究所",
            "id": null
          },
          {
            "name": "南京林业大学",
            "id": null
          }
        ],
        "sort": 5075
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "代塑用柔性薄型竹单板防霉及其强化成型关键技术开发",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "南平作为中国最大毛竹产区，开发柔性薄型竹单板防霉及强化成型技术，既能抓住全球\"以竹代塑\"的市场机遇，又能延伸产业链、提升产品附加值，同时实现环保效益和带动就业，是推动竹产业升级和绿色发展的关键突破口。",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "8eb0197ea8872af70453769e22fbbcae",
        "techDesc": "（1）原竹营养源定向拆分防霉关键技术，开发防霉型基本竹片单元。（2）薄型竹单板制造关键技术，优化确定蒸汽及微波软化、展平以及刨切工艺技术参数；（3）竹基材料增强增韧体系重构关键技术，重新构建竹基增强增韧复合材料微观网络结构；（4）竹纤维界面改性及材料结构性能评价，考察不同高分子增容剂、偶联剂等对复合材料的界面改性效果；（5）薄型竹单板强化成型工艺技术和“以竹代塑”新产品开发",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "浙江农林大学",
            "id": null
          }
        ],
        "sort": 5077
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "超级电容器用竹笋壳纳米纤维素基炭电极制备关键技术与产业应用",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "南平市作为中国重要竹产区，开发竹笋壳纳米纤维素基炭电极技术既能高效利用废弃笋壳资源（年处理量可达万吨级），又能制备高性能超级电容器电极材料，在降低电极材料成本同时延伸竹产业链，延伸竹产业经济效益，并减少传统电极材料生产带来的环境污染，是推动竹产业高值化转型和绿色发展的关键技术突破。",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "aabad4bf4f582560ff992d7f07195adf",
        "techDesc": "（1）微流体纺丝技术限域组装竹笋壳纤维素基炭电极孔道新工艺。（2）有限元仿真揭示限域孔道离子整流运输新技术。（3）基于离子整流的储能系统中竹笋壳纳米纤维素炭电极限域孔道的新机制。",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": null,
            "id": null
          }
        ],
        "sort": 5078
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "数字竹材分解点竹标准材连续加工智能生产关键技术与产业化",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "该技术通过自动化加工、精准控制和数字化管理，推动竹产业从劳动密集型向智能化转型，预计带动产业链增值，同时减少能耗，是提升区域竹产业竞争力的关键技术突破。",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "a7089e1b628f258ff547b8bb03bb0c7e",
        "techDesc": "1.首次应用觉机器人竹条智能分拣喂送料控制系统技术研究是现代化智能制造技术，解决行业公认的竹条喂送料共性关键技术难点。2.设计开发首台30吨/日处理量单位产能标准化竹材分解加工生产线，实现竹标准材可靠的连续化和智能化生产。3.面向竹产业加工体系发挥竹材分解点举措优势，研究一体化原竹伐集供需数字化平台应用，进一步深化功能优势，高效配置竹资源，提高资源利用率，促进现代竹产业生态构造。",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "中国林业科学研究院木材工业研究所",
            "id": null
          }
        ],
        "sort": 5079
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "高性能竹缠绕复合材料新型工程产品研发与应用",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "南平市研发高性能竹缠绕复合材料，将当地丰富的竹资源转化为高附加值工程产品，推动竹产业从传统加工向高端制造升级。该技术可替代部分钢材和塑料用量，预期可带动产业链年产值增长，创造新的就业岗位，同时减少碳排放，是实现竹产业绿色转型和高质量发展的关键技术突破。",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "d13446bf95176861851fda148429989e",
        "techDesc": "研发满足构件的截面形式要求，连续化制作工程产品的缠绕成型工艺和改进缠绕设备；需要解决两种材料之间协同变形和共同受力问题，需要研究材料间界面处理技术。",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "福建农林大学",
            "id": null
          }
        ],
        "sort": 5080
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "面向海上光伏支撑平台的高耐竹纤维重组工程材料的开发",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "南平市作为我国重要的竹林产区，拥有丰富的竹资源。利用这些资源开发高耐竹纤维重组工程材料，可以充分发挥当地资源优势，促进竹产业链的升级和转型。随着全球对可再生能源的需求不断增长，海上光伏发电作为一种清洁能源，其市场前景广阔。而海上光伏支撑平台对材料的要求较高，需要具有高强度、耐腐蚀、耐候性等特性。开发高耐竹纤维重组工程材料可以满足这一市场需求。目前，南平市竹产业主要以初级加工为主，附加值较低。通过攻关高耐竹纤维重组工程材料技术，可以实现竹材的深加工，提高产品附加值，推动产业链向高端延伸。高耐竹纤维重组工程材料相比传统金属材料，具有可再生、可降解、环保等优点。在海上光伏支撑平台的应用，有助于推动绿色能源的发展，符合国家环保政策导向。开发高耐竹纤维重组工程材料，可以降低海上光伏支撑平台的建设成本，提高项目投资回报率。同时，有助于南平市竹产业实现规模化、集约化生产，提高产业整体效益。通过攻关高耐竹纤维重组工程材料技术，南平市可以打造具有国际竞争力的竹木加工产业集群，提升产业在国际市场的竞争力。 高耐竹纤维重组工程材料的开发，需要科研机构、企业、高校等多方合作，推动技术创新，为南平市竹产业链发展注入新动力。",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "127c638762b6dc13d6f14d0831d6a5de",
        "techDesc": "核心关键技术问题：（1）霉变问题：霉变菌的生长繁殖除了消耗淀粉等物质，还会逐步降解纤维素和半纤维素，但对于木质化的细胞壁无显著影响，因此，霉变并不会造成竹材强度的显著下降。（2）光降解（老化）问题：竹材在光、水和氧气的共同作用下发生黄变、光泽度下降、粗糙度增加和开裂等老化现象则对竹材强度造成显著影响。紫外光辐射是竹材老化降解的主要原因。（3）尺寸稳定性问题：竹材的吸湿特性主要来源于两个方面：其一、竹材的主要成分为纤维素、半纤维素以及木质素，这三类物质含有大量的游离羟基，可以与水分子结合产生氢键，使水分被牢牢的锁定在其附近；其二、作为天然多孔材料，竹材具有丰富的毛细管结构，因此水分很容易通过孔隙被吸附，造成竹材吸湿膨胀。",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "河北工程大学",
            "id": null
          },
          {
            "name": "天津理工大学",
            "id": null
          }
        ],
        "sort": 5081
      },
      {
        "id": null,
        "chainId": null,
        "parentId": null,
        "level": null,
        "path": null,
        "name": "竹集成材连续化生产",
        "nameEn": null,
        "description": null,
        "isLeaf": null,
        "isWeak": null,
        "weakReason": "南平市拥有丰富的竹资源，尤其是毛竹资源。竹集成材连续化生产技术可以更高效地利用这些资源，提高竹材的利用率，减少浪费。传统的竹材加工方法通常效率较低，产品质量参差不齐。连续化生产技术能够提升竹材加工的自动化和智能化水平，有助于推动南平市竹产业链向高端制造升级。连续化生产技术通过自动化设备实现竹材的连续加工，能够大幅提高生产效率，降低人力成本，满足市场对竹材产品的需求。连续化生产技术可以保证生产过程的标准化，从而提高竹集成材的尺寸精度和表面质量，减少产品缺陷，提高用户满意度。连续化生产技术能够减少对环境的影响，比如减少胶粘剂的使用量，降低对环境的污染，符合可持续发展的要求。随着竹材加工技术的进步，南平市竹产业在国际市场上的竞争力将得到提升，有助于扩大出口，增加经济效益。攻关竹集成材连续化生产技术将促进相关领域的研发和创新，推动南平市竹产业的技术进步和产业升级。连续化生产技术需要配套的设备和工艺，这将带动相关设备制造、材料供应等相关产业的发展，形成产业集群效应。",
        "createTime": null,
        "updateTime": null,
        "isValid": null,
        "children": [],
        "properties": {},
        "dataId": "ff70cb5ada017ee41d1e93b7c02d69f6",
        "techDesc": "（1）竹集成材连续化生产工艺优化与自动化信息化系统构建技术研发；（2）连续化破竹与竹条上料粗刨一体化智能装备研发；（3）竹条连续化智能化精刨分选关键技术与装备研发；（4）竹集成材施胶组坯热压连续化只能装备技术研发。",
        "innovationScore": null,
        "companyNum": null,
        "techNum": null,
        "targetCompanyList": [
          {
            "name": "国际竹藤中心",
            "id": null
          },
          {
            "name": "中国林业科学研究院木材工业研究所",
            "id": null
          },
          {
            "name": "福建省林业科学研究院",
            "id": null
          },
          {
            "name": "福建帝视智能科技有限公司",
            "id": null
          },
          {
            "name": "福建省科乐达智能设备有限公司",
            "id": null
          }
        ],
        "sort": 5083
      }
    ]
  },
  "header": {
    "code": 200,
    "message": "success"
  }
}
```

## 四链融合总览-指标

**接口地址**:`/statistics/totalIndex`

**请求方式**:`POST`

**请求数据类型**:`application/json`

**响应数据类型**:`*/*`

**接口描述**: 获取四链融合总览指标数据

**请求示例**:

```json
{
  "appKey": 78965432101,
  "requestTime": "1755159168859",
  "sign": "100ff73bbb20a463a13270f08d0e12a7",
  "chainId": "1003"
}
```

**请求参数**:

| 参数名称                    | 参数说明               | 请求类型 | 是否必须 | 数据类型    | schema    |
| ----------------------- | ------------------ | ---- | ---- |---------| --------- |
| chainBO                 | chainBO            | body | true | 产业链查询条件 | 产业链查询条件 |
| &emsp;&emsp;appKey      | 登录认证key            |      | true | long    |           |
| &emsp;&emsp;chainId     | 产业链ID              |      | true | string  |           |
| &emsp;&emsp;requestTime | 当前时间戳              |      | true | string  |           |
| &emsp;&emsp;sign        | 签名值                |      | true | string  |           |

**响应状态**:

| 状态码 | 说明           | schema         |
| --- | ------------ | -------------- |
| 200 | OK           | 通用返回数据«总览指标» |
| 401 | Unauthorized |                |
| 403 | Forbidden    |                |
| 404 | Not Found    |                |

**响应参数**:

| 参数名称                      | 参数说明     | 类型             | schema |
|---------------------------|----------|----------------| ------ |
| body                      | 数据       | object         | 总览指标   |
| &emsp;&emsp;suggestion | 综合指数     | object         | 综合指数 |
| &emsp;&emsp;statisticMap  | 四链融合指标列表 | array          | 四联融合指标 |
| &emsp;&emsp;tendMap          | 指数变化趋势   | object         | 指数变化趋势 |
| &emsp;&emsp;fusionMap          | 绿色创新指数   | array         | 绿色创新指数 ||
| &emsp;&emsp;&emsp;&emsp;name          | 指标名称     | string         | 指标名称 |
| &emsp;&emsp;&emsp;&emsp;data          | 数值       | integer         | 数值 ||
| &emsp;&emsp;&emsp;&emsp;unit          | 单位       | string         | 单位 |
| header                    | 数据头      | 通用返回数据头        | 通用返回数据头 |
| &emsp;&emsp;code          | 状态码      | integer(int32) |        |
| &emsp;&emsp;message       | 状态码描述    | string         |        |

**响应示例**:

```json
{
  "body": {
    "suggestion": null,
    "statisticMap": [
      {
        "id": null,
        "name": "总节点",
        "data": 96,
        "unit": "个"
      },
      {
        "id": null,
        "name": "平台载体",
        "data": 66,
        "unit": "个"
      },
      {
        "id": null,
        "name": "企业技术中心",
        "data": 27,
        "unit": "个"
      },
      {
        "id": null,
        "name": "关键技术",
        "data": 31,
        "unit": "项"
      },
      {
        "id": null,
        "name": "省内有基础实现攻关突破",
        "data": 8,
        "unit": "个"
      },
      {
        "id": null,
        "name": "市内有基础实现攻关突破",
        "data": 20,
        "unit": "个"
      },
      {
        "id": null,
        "name": "已实现国产化替代",
        "data": 3,
        "unit": "个"
      },
      {
        "id": null,
        "name": "总企业数",
        "data": 6248,
        "unit": "家"
      },
      {
        "id": null,
        "name": "科技型中小企业",
        "data": 514,
        "unit": "家"
      },
      {
        "id": null,
        "name": "高新技术企业",
        "data": 653,
        "unit": "家"
      },
      {
        "id": null,
        "name": "科技小巨人企业",
        "data": 69,
        "unit": "家"
      },
      {
        "id": null,
        "name": "其他企业",
        "data": 5012,
        "unit": "家"
      },
      {
        "id": "1",
        "name": "强链",
        "data": 9,
        "unit": "个"
      },
      {
        "id": "2",
        "name": "补链",
        "data": 7,
        "unit": "个"
      },
      {
        "id": "3",
        "name": "固链",
        "data": 5,
        "unit": "个"
      },
      {
        "id": "4",
        "name": "拓链",
        "data": 10,
        "unit": "个"
      },
      {
        "id": null,
        "name": "本地项目",
        "data": 34,
        "unit": "个"
      },
      {
        "id": null,
        "name": "工信局项目",
        "data": 11,
        "unit": "个"
      },
      {
        "id": null,
        "name": "应用研究开发（工业、农业与社会发展等研究开发项目）",
        "data": 8,
        "unit": "个"
      },
      {
        "id": null,
        "name": "科技特派员创新创业大赛项目",
        "data": 5,
        "unit": "个"
      },
      {
        "id": null,
        "name": "其他项目",
        "data": 21,
        "unit": "个"
      },
      {
        "id": null,
        "name": "投融资",
        "data": 25692200,
        "unit": "万元"
      },
      {
        "id": null,
        "name": "本地人才",
        "data": 5730,
        "unit": "人"
      },
      {
        "id": null,
        "name": "硕士以上人才",
        "data": 5374,
        "unit": "人"
      },
      {
        "id": null,
        "name": "其他人才",
        "data": 356,
        "unit": "人"
      },
      {
        "id": null,
        "name": "项目补助资金",
        "data": 2192,
        "unit": "万元"
      },
      {
        "id": null,
        "name": "资金规模",
        "data": 25694392,
        "unit": "万元"
      },
      {
        "id": null,
        "name": "专利数",
        "data": 10000,
        "unit": "件"
      },
      {
        "id": null,
        "name": "发明专利",
        "data": 124,
        "unit": "件"
      },
      {
        "id": null,
        "name": "其他专利",
        "data": 9876,
        "unit": "件"
      }
    ],
    "tendMap": {},
    "fusionMap": [
      {
        "id": null,
        "name": "四链融合指数",
        "data": null,
        "unit": null
      },
      {
        "id": null,
        "name": "创新链指数",
        "data": null,
        "unit": null
      },
      {
        "id": null,
        "name": "资金链指数",
        "data": null,
        "unit": null
      },
      {
        "id": null,
        "name": "人才链指数",
        "data": null,
        "unit": null
      },
      {
        "id": null,
        "name": "产业链指数",
        "data": null,
        "unit": null
      }
    ]
  },
  "header": {
    "code": 200,
    "message": "success"
  }
}
```

## 指标详情

**接口地址**:`/statistics/index`

**请求方式**:`POST`

**请求数据类型**:`application/json`

**响应数据类型**:`*/*`

**接口描述**: 获取指定产业链的详细指标信息

**请求示例**:

```json
{
  "appKey": 78965432101,
  "requestTime": "1755159168859",
  "sign": "100ff73bbb20a463a13270f08d0e12a7",
  "chainId": "1003"
}
```

**请求参数**:

| 参数名称                    | 参数说明               | 请求类型 | 是否必须 | 数据类型    | schema    |
| ----------------------- | ------------------ | ---- | ---- |---------| --------- |
| chainBO                 | chainBO            | body | true | 产业链查询条件 | 产业链查询条件 |
| &emsp;&emsp;appKey      | 登录认证key            |      | true | long    |           |
| &emsp;&emsp;chainId     | 产业链ID              |      | true | string  |           |
| &emsp;&emsp;requestTime | 当前时间戳              |      | true | string  |           |
| &emsp;&emsp;sign        | 签名值                |      | true | string  |           |

**响应状态**:

| 状态码 | 说明           | schema         |
| --- | ------------ | -------------- |
| 200 | OK           | 通用返回数据«指标详情» |
| 401 | Unauthorized |                |
| 403 | Forbidden    |                |
| 404 | Not Found    |                |

**响应参数**:

| 参数名称                   | 参数说明   | 类型             | schema  |
| ---------------------- |--------|----------------| ------- |
| body                   | 数据     | object         | 指标详情    |
| &emsp;&emsp;techPoint | 四链融合指数 | float          | 四链融合指数 |
| &emsp;&emsp;increase | 环比   | float          | 环比 |
| &emsp;&emsp;patentNum | 专利数量   | integer        | 专利数量 |
| &emsp;&emsp;companyNum | 链上企业   | integer        | 链上企业 |
| &emsp;&emsp;financeAmount | 投融资规模   | float           | 投融资规模 |
| &emsp;&emsp;financeNum | 投融资笔数   | integer         | 投融资笔数 |
| &emsp;&emsp;localNum | 本地人才   | integer         | 本地人才 |
| header                 | 数据头    | 通用返回数据头        | 通用返回数据头 |
| &emsp;&emsp;code       | 状态码    | integer(int32) |         |
| &emsp;&emsp;message    | 状态码描述  | string         |         |

**响应示例**:

```json
{
  "body": {
    "techPoint": 82.61,
    "increase": 1.100,
    "patentNum": 463,
    "companyNum": 919,
    "financeAmount": null,
    "financeNum": 4,
    "localNum": 303
  },
  "header": {
    "code": 200,
    "message": "success"
  }
}
```

## 四链融合模型

**接口地址**:`/statistics/fusion`

**请求方式**:`POST`

**请求数据类型**:`application/json`

**响应数据类型**:`*/*`

**接口描述**: 获取四链融合模型的详细数据

**请求示例**:

```json
{
  "appKey": 78965432101,
  "requestTime": "1755159168859",
  "sign": "100ff73bbb20a463a13270f08d0e12a7",
  "chainId": "1003"
}
```

**请求参数**:

| 参数名称                    | 参数说明               | 请求类型 | 是否必须 | 数据类型           | schema    |
| ----------------------- | ------------------ | ---- | ---- | -------------- | --------- |
| chainBO                 | chainBO            | body | true | 产业链查询条件        | 产业链查询条件 |
| &emsp;&emsp;appKey      | 登录认证key            |      | true | long |           |
| &emsp;&emsp;chainId     | 产业链ID              |      | true | string         |           |
| &emsp;&emsp;requestTime | 当前时间戳              |      | true | string         |           |
| &emsp;&emsp;sign        | 签名值                |      | true | string         |           |

**响应状态**:

| 状态码 | 说明           | schema         |
| --- | ------------ | -------------- |
| 200 | OK           | 通用返回数据«融合模型» |
| 401 | Unauthorized |                |
| 403 | Forbidden    |                |
| 404 | Not Found    |                |

**响应参数**:

| 参数名称                   | 参数说明   | 类型             | schema  |
| ---------------------- |--------|----------------|---------|
| body                   | 数据     | object         | 融合模型    |
| &emsp;&emsp;id | id     | number         |         |
| &emsp;&emsp;chainId | 产业链id  | string         | 产业链     |
| &emsp;&emsp;chainNodeId | 产业节点id | string         | 产业节点    |
| &emsp;&emsp;data | 数值     | float          | 数值      |
| &emsp;&emsp;description | 分析与建议  | string         | 分析与建议      |
| header                 | 数据头    | 通用返回数据头        | 通用返回数据头 |
| &emsp;&emsp;code       | 状态码    | integer(int32) |         |
| &emsp;&emsp;message    | 状态码描述  | string         |         |

**响应示例**:

```json
{
  "body": {
    "综合指数": {
      "id": 709,
      "chainId": "1003",
      "chainNodeId": "instance_concept_node_np_bamboo_technology",
      "regionId": "division/000350700",
      "indexId": "20000000",
      "indexName": null,
      "statTime": "2024-12-01 00:00:00",
      "data": 82.61,
      "description": null
    },
    "创新链": {
      "id": 783,
      "chainId": "1003",
      "chainNodeId": "instance_concept_node_np_bamboo_technology",
      "regionId": "division/000350700",
      "indexId": "20010000",
      "indexName": null,
      "statTime": "2024-12-01 00:00:00",
      "data": 72.35,
      "description": "南平竹产业正在积极探索创新，推出了电子商务交易及信息互换平台，运用区块链的设计逻辑为用户提供竹砍伐、加工、运输、购销等环节的交易和运作。商业模式创新之外，南平竹产业专利增速领先于闽浙赣平均水平，但专利存量相对不足，研发创新投入有待加强。"
    },
    "资金链": {
      "id": 786,
      "chainId": "1003",
      "chainNodeId": "instance_concept_node_np_bamboo_technology",
      "regionId": "division/000350700",
      "indexId": "20040000",
      "indexName": null,
      "statTime": "2024-12-01 00:00:00",
      "data": 71.61,
      "description": "南平竹产业发展得到了金融机构的大力支持，已设立产业绿色金融信贷风险资金池，鼓励金融机构扩大竹产业贷款规模。南平竹产业在融资总额、融资成本、融资约束等指标上波动较大，低于闽浙赣均值，需进一步发展绿色信贷产品，增大竹产业二产的资金投入，积极招商引资。"
    },
    "人才链": {
      "id": 785,
      "chainId": "1003",
      "chainNodeId": "instance_concept_node_np_bamboo_technology",
      "regionId": "division/000350700",
      "indexId": "20030000",
      "indexName": null,
      "statTime": "2024-12-01 00:00:00",
      "data": 77.32,
      "description": "南平竹产业积极引进和培养人才，依托技工院校，为林产工业重点培育企业开办校企合作人才专班，组织龙头企业开展新型学徒制。南平主动对接中国林科院、国际竹藤中心、林业高等院校，促进竹产业关键技术、新产品研发和成果落地。但南平高端人才相对不足，亟需进一步加强人才招引。"
    },
    "产业链": {
      "id": 784,
      "chainId": "1003",
      "chainNodeId": "instance_concept_node_np_bamboo_technology",
      "regionId": "division/000350700",
      "indexId": "20020000",
      "indexName": null,
      "statTime": "2024-12-01 00:00:00",
      "data": 94.04,
      "description": "南平竹产业链已相对完善，形成了上游竹拉丝、竹片，中游竹胶板、重竹板，下游竹家具、竹工艺品的关联性产业集群。南平还致力于全竹利用、产业出新，实现了竹炭硅三大产业循环。南平竹产业基础较好，在企业总数、就业人员、产业总产值等指标上领跑闽浙赣各城市。南平竹产业链已相对完善，形成了上游竹拉丝、竹片，中游竹胶板、重竹板，下游竹家具、竹工艺品的关联性产业集群。南平还致力于全竹利用、产业出新，实现了竹炭硅三大产业循环。南平竹产业基础较好，在企业总数、就业人员、产业总产值等指标上领跑闽浙赣各城市。"
    }
  },
  "header": {
    "code": 200,
    "message": "success"
  }
}
```