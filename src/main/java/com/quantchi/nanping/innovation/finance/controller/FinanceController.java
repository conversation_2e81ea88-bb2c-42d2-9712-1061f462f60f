package com.quantchi.nanping.innovation.finance.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quantchi.anti.annotation.AntiReptile;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.config.aop.Log;
import com.quantchi.nanping.innovation.config.aop.Metrics;
import com.quantchi.nanping.innovation.config.aop.PlatformAuthCheck;
import com.quantchi.nanping.innovation.demand.model.Demand;
import com.quantchi.nanping.innovation.demand.model.enums.DemandTypeEnum;
import com.quantchi.nanping.innovation.demand.model.enums.ProcedureEnum;
import com.quantchi.nanping.innovation.demand.service.IDemandMngService;
import com.quantchi.nanping.innovation.finance.model.FinanceLocalOrg;
import com.quantchi.nanping.innovation.finance.model.FinanceNeed;
import com.quantchi.nanping.innovation.finance.service.IFinanceLocalOrgService;
import com.quantchi.nanping.innovation.finance.service.IFinanceNeedService;
import com.quantchi.nanping.innovation.finance.service.IFinanceProductService;
import com.quantchi.nanping.innovation.model.bo.FinanceEventBo;
import com.quantchi.nanping.innovation.model.constant.CommonConstant;
import com.quantchi.nanping.innovation.service.library.FinanceService;
import com.quantchi.nanping.innovation.service.library.ProjectService;
import com.quantchi.nanping.innovation.utils.AESUtil;
import com.quantchi.nanping.innovation.utils.RequestContext;
import com.quantchi.tianying.model.EsPageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/27 10:13 上午
 * @description
 */
@Slf4j
@RestController
@RequestMapping("/finance")
@Api(tags = "科技金融")
@PlatformAuthCheck(type = {"0"})
@Metrics
public class FinanceController {

    @Autowired
    private FinanceService financeService;

    @Autowired
    private IFinanceLocalOrgService localOrgService;

    @Autowired
    private IFinanceProductService productService;

    @Autowired
    private IDemandMngService demandMngService;

    @Autowired
    private IFinanceNeedService financeNeedService;

    @Autowired
    private ProjectService projectService;

    @PostMapping("/get/event/list")
    @ApiOperation("投融资事件")
    @Log(title = "科技金融")
    public Result getEventList(@Validated @RequestBody FinanceEventBo financeEventBo) {
        if ("1009".equals(financeEventBo.getChainId())) {
            return ResultConvert.success(financeService.getEventListTimeLimit(financeEventBo));
        }
        financeEventBo.setCityId(RequestContext.getCityId());
        financeEventBo.setAreaId(RequestContext.getAreaId());
        return ResultConvert.success(financeService.getEventList(financeEventBo));
    }

    @GetMapping("/get/investment/fund")
    @ApiOperation("获取投资机构")
    //@Log(title = "科技金融-获取投资机构")
    public Result getInvestmentFund(String chainId) {
        return ResultConvert.success(financeService.getInvestmentFund(chainId));
    }

    @GetMapping("/get/investment/local")
    @ApiOperation("获取本地布局投资机构")
    //@Log(title = "科技金融-获取本地布局投资机构")
    public Result getLocalInvestment() {
        return ResultConvert.success(localOrgService.list(Wrappers.lambdaQuery(FinanceLocalOrg.class).orderByAsc(FinanceLocalOrg::getId)));
    }

    @GetMapping("/get/area/fund")
    @ApiOperation("获取区域投融资对比")
    //@Log(title = "科技金融-获取区域投融资对比")
    @Deprecated
    public Result getAreaFund() {
        return ResultConvert.success(financeService.getAreaFund());
    }

    @GetMapping("/get/fund/statistic")
    @ApiOperation("科技企业、传统企业资金对比")
    //@Log(title = "科技金融-科技企业、传统企业资金对比")
    public Result getFundStatistic(String indexType) {
        return ResultConvert.success(financeService.getFundStatistic(indexType));
    }

    @GetMapping("/get/year/finance/info")
    @ApiOperation("获取年度投融资概览情况")
    //@Log(title = "科技金融-获取年度投融资概览情况")
    public Result getYearFinanceInfo(String year) {
        return ResultConvert.success(financeService.getYearFinanceInfo(year, RequestContext.getCityId(), RequestContext.getAreaId()));
    }

    @GetMapping("/get/finance/info")
    @ApiOperation("投融资指标监测")
    public Result getFinanceInfo(@NotBlank String chainId) {
        return ResultConvert.success(financeService.getFinanceInfo(chainId, RequestContext.getCityId(), null));
    }

    @GetMapping("/get/fund/chain")
    @ApiOperation("资金分布情况")
    //@Log(title = "科技金融-资金分布情况")
    public Result getFundDistributionInChain(String indexType) {
        return ResultConvert.success(financeService.getFundDistributionInChain(indexType));
    }

    @GetMapping("/get/fund/node")
    @ApiOperation("资金分布情况(节点)")
    public Result getFundDistributionInNode(String indexType, String chainId) {
        return ResultConvert.success(financeService.getFundDistributionInNode(indexType, chainId));
    }

    @GetMapping("/get/product")
    @ApiOperation("金融产品和投资机构")
    //@Log(title = "科技金融-金融产品和投资机构")
    public Result getProductAndOrg(String chainId) {
        chainId = null;
        return ResultConvert.success(financeService.listProductAndOrg(chainId));
    }

    @GetMapping("/get/fund/acquisition")
    @ApiOperation("资金获取情况")
    //@Log(title = "资金获取情况")
    public Result listFundAcquisition(String chainId, boolean fromCockpit) {
        return ResultConvert.success(financeService.listFundAcquisition(chainId, fromCockpit));
    }

    @GetMapping("/get/fund/need")
    @ApiOperation("企业融资需求")
    public Result<List<FinanceNeed>> listFundNeed(@NotBlank String chainId, boolean fromCockpit) {
        // 资金需求 demand
        List<Demand> demandList = demandMngService.listByType(chainId, DemandTypeEnum.FUND.getName(), ProcedureEnum.START.getId());
        List<FinanceNeed> resultList = new ArrayList<>();
        for (Demand d: demandList){
            FinanceNeed fn = new FinanceNeed();
            fn.setCompanyName(d.getUserName());
            fn.setFinanceType(d.getFinanceType());
            if (d.getApplyAmount() != null){
                fn.setAmount(new BigDecimal(d.getApplyAmount()));
            }
            fn.setStatus(ProcedureEnum.getNameById(d.getStatus()));
            resultList.add(fn);
        }
        // 企业融资需求 finance_need
        List<FinanceNeed> financeNeeds = financeNeedService.listByChainId(chainId);
        financeNeeds.forEach(f -> f.setStatus(ProcedureEnum.END_SUCCESS.getName()));
        resultList.addAll(financeNeeds);
        // 脱敏处理
        if (fromCockpit){
            resultList.forEach(f -> {
                f.setCompanyName(financeService.desensitizeCompanyName(f.getCompanyName()));
            });
        }
        return ResultConvert.success(resultList);
    }

    @GetMapping("/get/subsidy/list")
    @ApiOperation("项目补助列表")
    public Result getEventList(@NotBlank String chainId, @RequestParam(defaultValue = "1") Integer pageNum, @RequestParam(defaultValue = "5") Integer pageSize,
                               String companyName, String projectType, String projectName) {
        return ResultConvert.success(projectService.page4Subsidy(pageNum, pageSize, chainId, null, CommonConstant.DIVISION_NANPING.getId(), RequestContext.getAreaId(), companyName, projectType, projectName));
    }

    @GetMapping("/get/loan/list")
    @ApiOperation("贷款发放列表")
    public Result getLoanList(@NotBlank String chainId, @RequestParam(defaultValue = "1") Integer pageNum, @RequestParam(defaultValue = "5") Integer pageSize) {
        Page<Map<String, Object>> detailPage = financeService.page4Loan(pageNum, pageSize, chainId);
        if (CollectionUtils.isNotEmpty(detailPage.getRecords())){
            for (Map<String, Object> record: detailPage.getRecords()){
                String creditObjectName = AESUtil.decrypt((String) record.get("credit_object"));
                // 字段脱敏
                if (creditObjectName.length() > 3 ){
                    creditObjectName = financeService.desensitizeCompanyName(creditObjectName);
                }else{
                    // 个人
                    creditObjectName = StringUtils.rightPad(creditObjectName.substring(0, 1), creditObjectName.length(), '*');
                }
                record.put("credit_object", creditObjectName);
            }
        }
        EsPageResult pageResult = new EsPageResult();
        pageResult.setList(detailPage.getRecords());
        pageResult.setTotal(detailPage.getTotal());
        pageResult.setPageSize(pageSize);
        return ResultConvert.success(pageResult);
    }

}
