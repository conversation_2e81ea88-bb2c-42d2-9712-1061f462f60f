package com.quantchi.nanping.innovation.external.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.common.exception.MessageException;
import com.quantchi.nanping.innovation.company.service.ITechnicalContractService;
import com.quantchi.nanping.innovation.config.aop.Log;
import com.quantchi.nanping.innovation.config.aop.Metrics;
import com.quantchi.nanping.innovation.config.aop.RateLimiter;
import com.quantchi.nanping.innovation.controller.CockpitController;
import com.quantchi.nanping.innovation.controller.TechCommissionController;
import com.quantchi.nanping.innovation.controller.TechInvestAttractController;
import com.quantchi.nanping.innovation.external.model.bo.*;
import com.quantchi.nanping.innovation.finance.controller.FinanceController;
import com.quantchi.nanping.innovation.insight.controller.OverViewController;
import com.quantchi.nanping.innovation.insight.service.IIndexFusionService;
import com.quantchi.nanping.innovation.knowledge.center.service.IKnowledgeCenterQueryService;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.enums.BusinessType;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.model.enums.ResultCodeEnum;
import com.quantchi.nanping.innovation.model.vo.PieVO;
import com.quantchi.tianying.model.MultidimensionalQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Validated
@RequiredArgsConstructor
@RestController
@Slf4j
@Api(tags = "统计信息")
@RequestMapping("/statistics")
public class StatisticsController {

    private final IKnowledgeCenterQueryService knowledgeCenterQueryService;
    private final CockpitController cockpitController;
    private final TechCommissionController techCommissionController;
    private final FinanceController financeController;
    private final TechInvestAttractController techInvestAttractController;
    private final ITechnicalContractService contractService;
    private final OverViewController overViewController;
    private final IIndexFusionService indexFusionService;



    @ApiOperation("查询产业专家人才")
    @PostMapping("/filtering")
    @RateLimiter(key = "rate_limit:expert_query:", count = 60)
    public Result queryExperts(@RequestBody final QueryExpertsBO bo) {
        calibratePermission(bo);
        MultidimensionalQuery mQuery = new MultidimensionalQuery();
        mQuery.setIndex(EsIndexEnum.EXPERT.getEsIndex());
        mQuery.setPageNum(bo.getPageNum());
        mQuery.setPageSize(bo.getPageSize());
        mQuery.setKeyword(bo.getKeyword());
        try {
            return ResultConvert.success(knowledgeCenterQueryService.queryByTermsAndKey(mQuery, false, bo.getNodePath()));
        } catch (Exception e) {
            log.error("查询专家失败", e);
            return ResultConvert.error(ResultConvert.ERROR_CODE, "查询失败");
        }
    }

    @ApiOperation(value = "产业链-链上企业分布")
    @PostMapping("/industry/company")
    public Result<Map<String, Object>> industryDetail(@RequestBody final ChainBO bo) {
        calibratePermission(bo);
        return cockpitController.industryDetail(bo.getChainId(), "");
    }

    @ApiOperation(value = "产业链-链节点分布-圆环图统计")
    @PostMapping("/industry/node/distribution")
    public Result<PieVO> getNodeDistribution(@RequestBody final ChainBO bo) {
        calibratePermission(bo);
        return cockpitController.getNodeDistribution(bo.getChainId());
    }

    @ApiOperation("人才学历、职称分布")
    @PostMapping("/degree_title/distribution")
    public Result<Map<String, Object>> getDegreeTitleDistribution(@RequestBody final ChainBO bo) {
        calibratePermission(bo);
        return techCommissionController.getDegreeTitleDistribution(bo.getChainId());
    }

    @ApiOperation("外部人才节点分布")
    @PostMapping("/external/node/distribution")
    public Result<PieVO> getExternalNodeDistribution(@RequestBody final ChainBO bo) {
        calibratePermission(bo);
        return  techCommissionController.getExternalNodeDistribution(bo.getChainId());
    }


    @PostMapping("/get/finance/info")
    @ApiOperation("投融资指标监测")
    public Result getFinanceInfo(@RequestBody final ChainBO bo) {
        calibratePermission(bo);
        return financeController.getFinanceInfo(bo.getChainId());
    }

    @PostMapping("/get/subsidy/list")
    @ApiOperation("项目补助列表")
    public Result getEventList(@RequestBody final SubsidyBO bo){
        calibratePermission(bo);
        return financeController.getEventList(bo.getChainId(), bo.getPageNum(), bo.getPageSize(), bo.getCompanyName(), null, bo.getProjectName());
    }

    @PostMapping("/weak/type")
    @ApiOperation("关键核心技术分类统计")
    public Result<List<CommonIndexBO>> getRiskMap(@RequestBody final ChainBO bo) {
        calibratePermission(bo);
        return techInvestAttractController.getRiskMap(bo.getChainId());
    }

    @ApiOperation("技术合同成果交易统计")
    @PostMapping("/ach_trade/count")
    public Result count(@RequestBody final BaseThirdQuery bo) {
        calibratePermission(bo);
        return ResultConvert.success(contractService.countByType());
    }

    @ApiOperation(value = "创新链诊断")
    @PostMapping("/chain_analyze")
    public Result<Map<String, Object>> analyzeInnovationChain(@RequestBody final ChainBO bo) {
        calibratePermission(bo);
        return techInvestAttractController.analyzeInnovationChain(bo.getChainId());
    }

    @PostMapping("/totalIndex")
    @ApiOperation("四链融合总览-指标")
    public Result totalIndex(@RequestBody final ChainBO bo) {
        calibratePermission(bo);
        return overViewController.totalIndex(bo.getChainId());
    }

    @ApiOperation(value = "指标")
    @PostMapping("/index")
    public Result<Map<String, Object>> indexDetail(@RequestBody final  ChainBO bo) {
        calibratePermission(bo);
        return cockpitController.indexDetail(bo.getChainId(), null);
    }

    @ApiOperation(value = "四链融合模型")
    @PostMapping("/fusion")
    public Result<Map<String, Object>> fusionDetail(@RequestBody final ChainBO bo) {
        calibratePermission(bo);
        return ResultConvert.success(indexFusionService.fusionDetail(bo.getChainId()));
    }



    public static void main(String[] args) {
        String requestTime = System.currentTimeMillis() + "";
        System.out.println(requestTime);
        System.out.println(DigestUtils.md5Hex(tempAppKey + appSecret + requestTime));
    }

    /**
     * 专用AppKey
     */
    public static final Long tempAppKey = 87965432301L;

    /**
     * 专用AppSecret
     */
    public static final String appSecret = "gO4qBTQSirpFXeU2Hth";

    /**
     * 校验权限
     */
    private void calibratePermission(final BaseThirdQuery baseThirdQuery) {
        // 校验用户token
        final Long appKey = baseThirdQuery.getAppKey();
        final String requestTime = baseThirdQuery.getRequestTime();
        final String sign = baseThirdQuery.getSign();

        if (!Objects.equals(appKey, tempAppKey)) {
            throw new MessageException(ResultCodeEnum.USER_ERROR, "appKey错误");
        }

        // 可以根据需要取消注释以下代码，增加时间戳验证
        if (System.currentTimeMillis() - Long.parseLong(requestTime) > 15 * 60 * 1000) {
            throw new MessageException(ResultCodeEnum.USER_ERROR, "时间戳已过期");
        }

        // IP白名单验证也可以根据需要添加
        // final String whiteIp = user.getPassword();
        // if (whiteIp == null || !whiteIp.contains(clientIP)) {
        //     throw new MessageException(ResultCodeEnum.USER_ERROR, "不在IP白名单中");
        // }

        if (!DigestUtils.md5Hex(appKey + appSecret + requestTime).equals(sign)) {
            throw new MessageException(ResultCodeEnum.USER_ERROR, "签名错误");
        }
    }
}
