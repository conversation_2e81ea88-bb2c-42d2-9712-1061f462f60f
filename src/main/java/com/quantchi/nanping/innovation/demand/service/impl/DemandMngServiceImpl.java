package com.quantchi.nanping.innovation.demand.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kind.crypto.common.ConfigBean;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.common.exception.BusinessException;
import com.quantchi.nanping.innovation.demand.dao.*;
import com.quantchi.nanping.innovation.demand.model.*;
import com.quantchi.nanping.innovation.demand.model.bo.ProcessPageBO;
import com.quantchi.nanping.innovation.demand.model.bo.SortColumn;
import com.quantchi.nanping.innovation.demand.model.enums.DemandTypeEnum;
import com.quantchi.nanping.innovation.demand.model.enums.ProcedureEnum;
import com.quantchi.nanping.innovation.demand.model.vo.DemandStatistics;
import com.quantchi.nanping.innovation.demand.service.IDemandConfigService;
import com.quantchi.nanping.innovation.demand.service.IDemandMngService;
import com.quantchi.nanping.innovation.finance.dao.FinanceLoanDetailMapper;
import com.quantchi.nanping.innovation.finance.model.FinanceLoanDetail;
import com.quantchi.nanping.innovation.overcome.model.bo.EvaluationQuery;
import com.quantchi.nanping.innovation.overcome.service.OvercomeService;
import com.quantchi.nanping.innovation.service.IFileService;
import com.quantchi.nanping.innovation.model.FileInfo;
import com.quantchi.nanping.innovation.model.IndustryChainNode;
import com.quantchi.nanping.innovation.model.UserInfoEntity;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.demand.model.bo.DemandBO;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.model.vo.PieVO;
import com.quantchi.nanping.innovation.service.ISysUserChainScopeService;
import com.quantchi.nanping.innovation.service.IThirdFinanceService;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.service.impl.SysLoginService;
import com.quantchi.nanping.innovation.service.library.CompanyService;
import com.quantchi.nanping.innovation.service.library.DemandService;
import com.quantchi.nanping.innovation.service.library.PatentService;
import com.quantchi.nanping.innovation.utils.DateUtils;
import com.quantchi.nanping.innovation.utils.EncryptUtil;
import com.quantchi.nanping.innovation.utils.StringRedisCache;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/23 18:13
 */
@Service
@Slf4j
public class DemandMngServiceImpl implements IDemandMngService {

    @Autowired
    private DemandService demandService;

    @Autowired
    private DemandMapper demandMapper;

    @Autowired
    private SysLoginService sysLoginService;

    @Autowired
    private IFileService fileService;

    @Autowired
    private DemandProcessMapper processMapper;

    @Autowired
    private DemandEvaluationMapper demandEvaluationMapper;

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @Autowired
    private IndustryChainService chainService;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private DemandUndertakeMapper undertakeMapper;

    @Autowired
    private OvercomeService overcomeService;

    @Autowired
    private ISysUserChainScopeService chainScopeService;

    @Autowired
    private FinanceLoanDetailMapper loanDetailMapper;

    @Autowired
    private DemandConfigMapper demandConfigMapper;

    @Autowired
    private PatentService patentService;

    @Autowired
    private StringRedisCache redisCache;

    @Autowired
    private IThirdFinanceService thirdFinanceService;

    @Autowired
    private IDemandConfigService demandConfigService;

    @Override
    public DemandStatistics getStatistics(String creditCode) throws Exception {
        DemandStatistics statistics = new DemandStatistics();
        Set<String> chainIds = chainScopeService.getCurrentUserPermittedChainIds();
        statistics.setTotalNum(demandMapper.selectCount(Wrappers.lambdaQuery(Demand.class)
                .eq(StringUtils.isNotEmpty(creditCode), Demand::getUserId, creditCode)
                .in(CollectionUtils.isNotEmpty(chainIds), Demand::getChainId, chainIds)));
        statistics.setTodayNum(demandMapper.selectCount(Wrappers.lambdaQuery(Demand.class)
                .ge(Demand::getCreateTime, DateUtils.format2yyyyMMdd(new Date()))
                .eq(StringUtils.isNotEmpty(creditCode), Demand::getUserId, creditCode)
                .in(CollectionUtils.isNotEmpty(chainIds), Demand::getChainId, chainIds)));
        statistics.setNotStartNum(demandMapper.selectCount(Wrappers.lambdaQuery(Demand.class)
                .eq(Demand::getStatus, ProcedureEnum.NOT_START.getId())
                .eq(StringUtils.isNotEmpty(creditCode), Demand::getUserId, creditCode)
                .in(CollectionUtils.isNotEmpty(chainIds), Demand::getChainId, chainIds)));
        statistics.setProcessingNum(demandMapper.selectCount(Wrappers.lambdaQuery(Demand.class)
                .gt(Demand::getStatus, ProcedureEnum.NOT_START.getId())
                .lt(Demand::getStatus, ProcedureEnum.END_SUCCESS.getId())
                .eq(StringUtils.isNotEmpty(creditCode), Demand::getUserId, creditCode)
                .in(CollectionUtils.isNotEmpty(chainIds), Demand::getChainId, chainIds)));
        statistics.setToEvaluateNum(demandMapper.selectCount(Wrappers.lambdaQuery(Demand.class)
                .ge(Demand::getStatus, ProcedureEnum.END_SUCCESS.getId())
                .eq(StringUtils.isNotEmpty(creditCode), Demand::getUserId, creditCode)
                .in(CollectionUtils.isNotEmpty(chainIds), Demand::getChainId, chainIds)));
        return statistics;
    }

    @Override
    public List<CommonIndexBO> countByChain() {
        Set<String> chainIds = chainScopeService.getCurrentUserPermittedChainIds();
        List<Demand> demands = demandMapper.selectList(Wrappers.lambdaQuery(Demand.class).select(Demand::getId, Demand::getChainName)
                .in(CollectionUtils.isNotEmpty(chainIds), Demand::getChainId, chainIds));
        Map<String, Long> countMap = demands.stream().collect(Collectors.groupingBy(Demand::getChainName, Collectors.counting()));
        return CommonIndexBO.buildList(countMap);
    }

    @Override
    public Page<Demand> page(DemandBO bo) {
        Set<String> chainIds = chainScopeService.getCurrentUserPermittedChainIds();
        
        // 创建查询条件
        final LambdaQueryWrapper<Demand> queryWrapper = Wrappers.lambdaQuery(Demand.class)
                .select(Demand::getId, Demand::getUserName, Demand::getUserId, Demand::getChainName, Demand::getType, Demand::getStatus, Demand::getTitle, Demand::getCreateTime,
                        Demand::getUpdateTime, Demand::getRemark, Demand::getDemandSort)
                .eq(StringUtils.isNotBlank(bo.getType()), Demand::getType, bo.getType())
                .eq(bo.getStatusId() != null && ProcedureEnum.REJECT.getId().equals(bo.getStatusId()), Demand::getStatus, bo.getStatusId())
                .eq(bo.getStatusId() != null && ProcedureEnum.NOT_START.getId().equals(bo.getStatusId()), Demand::getStatus, bo.getStatusId())
                .ge(bo.getStatusId() != null && ProcedureEnum.START.getId().equals(bo.getStatusId()), Demand::getStatus, ProcedureEnum.START.getId())
                .lt(bo.getStatusId() != null && ProcedureEnum.START.getId().equals(bo.getStatusId()), Demand::getStatus, ProcedureEnum.END_SUCCESS.getId())
                .eq(bo.getStatusId() != null && ProcedureEnum.END_SUCCESS.getId().equals(bo.getStatusId()), Demand::getStatus, ProcedureEnum.END_SUCCESS.getId())
                .eq(bo.getStatusId() != null && ProcedureEnum.END_FAIL.getId().equals(bo.getStatusId()), Demand::getStatus, ProcedureEnum.END_FAIL.getId())
                //.eq(bo.getStatusId() != null && ProcedureEnum.LENDING_SUCCESS.getId().equals(bo.getStatusId()), Demand::getStatus, ProcedureEnum.LENDING_SUCCESS.getId())
                .ge(bo.getStatusId() != null && ProcedureEnum.EVALUATE.getId().equals(bo.getStatusId()), Demand::getStatus, ProcedureEnum.END_SUCCESS.getId())
                .le(StringUtils.isNotBlank(bo.getCommitDateTo()), Demand::getCreateTime, bo.getCommitDateTo() + " 23:59:59")
                .ge(StringUtils.isNotBlank(bo.getCommitDateFrom()), Demand::getCreateTime, bo.getCommitDateFrom())
                .like(StringUtils.isNotBlank(bo.getProposer()), Demand::getUserName, bo.getProposer())
                .eq(StringUtils.isNotBlank(bo.getProposerId()), Demand::getUserId, bo.getProposerId())
                .eq(StringUtils.isNotBlank(bo.getChainId()), Demand::getChainId, bo.getChainId())
                .in(StringUtils.isEmpty(bo.getChainId()) && CollectionUtils.isNotEmpty(chainIds), Demand::getChainId, chainIds)
                .like(StringUtils.isNotBlank(bo.getTitle()), Demand::getTitle, bo.getTitle())
                .last("order by demand_sort desc, " + SortColumn.getSortRuleList(bo.getSort()));
        
        // 临时查看SQL语句
        String sqlSegment = queryWrapper.getCustomSqlSegment();
        log.info("生成的SQL条件语句: {}", sqlSegment);
        log.info("SQL参数: {}", queryWrapper.getParamNameValuePairs());
        
        Page<Demand> pageResult = demandMapper.selectPage(new Page<>(bo.getPageNum(), bo.getPageSize()), queryWrapper);
        List<Demand> demandList = pageResult.getRecords();
        if (CollectionUtils.isNotEmpty(demandList)) {
            // 判断是否为服务侧查看，则不展示备注字段
            UserInfoEntity currentUser = sysLoginService.getUserInfo(false);
            boolean displayRemark = UserInfoEntity.PLATFORM_GOV != currentUser.getPlatformType();
            for (Demand demand : demandList) {
                if (ProcedureEnum.END_SUCCESS.getId().compareTo(demand.getStatus()) <= 0) {
                    demand.setFinalStatus(ProcedureEnum.getNameById(demand.getStatus()));
                }
                if (displayRemark) {
                    demand.setRemark(null);
                }
                // 返回companyId
                if (!Demand.NONE_FILL.equals(demand.getUserId())) {
                    Map<String, Object> companyInfo = companyService.findCompanyByCreditCode(demand.getUserId());
                    if (companyInfo.isEmpty()) {
                        demand.setCompanyId(demand.getUserId());
                    } else {
                        demand.setCompanyId((String) companyInfo.get("id"));
                    }
                } else {
                    demand.setCompanyId(Demand.NONE_FILL);
                }
            }
        }
        log.info("params: {}", bo);
        log.info("demandList: {}", demandList);
        return pageResult;
    }

    @Override
    public Demand getById(String id) {
        Demand demand = findExistedDemand(id);
        DemandConfig demandConfig = demandConfigMapper.selectOne(Wrappers.lambdaQuery(DemandConfig.class)
                .eq(DemandConfig::getDemandId, id));
        // 解密手机号
        demand.setLink(EncryptUtil.decryptSM4FixIndex(demand.getLink(), demand.getEncryptIndex()));
        if (DemandTypeEnum.TALENT.getName().equals(demand.getType()) || DemandTypeEnum.TECH.getName().equals(demand.getType())) {
            // 补充推荐专家
            //demand.setRecommendExpertList(demandService.fillMatchedExpert(demand.getChainId(), demand.getChainNodeId(), id, demand.getContent(), demandConfig));
            // 补充自荐专家
            List<DemandUndertake> selfRecommendList = undertakeMapper.selectList(Wrappers.lambdaQuery(DemandUndertake.class)
                    .eq(DemandUndertake::getDemandId, id)
                    .orderByAsc(DemandUndertake::getCreateTime));
            if (CollectionUtils.isNotEmpty(selfRecommendList)) {
                // 解密手机号
                for (DemandUndertake undertake : selfRecommendList) {
                    undertake.setLink(EncryptUtil.decryptSM4FixIndex(undertake.getLink(), undertake.getEncryptIndex()));
                }
                demand.setSelfRecommendExpertList(selfRecommendList);
            }
            if (DemandTypeEnum.TECH.getName().equals(demand.getType())) {
                // 补充推荐企业
                List<Map<String, Object>> recommendCompanyList = companyService.getCompanyList4Demand(demand.getChainNodeId());
                if (CollectionUtils.isNotEmpty(recommendCompanyList)) {
                    demand.setRecommendCompanyList(recommendCompanyList);
                }
            }
//            // 补充推荐专利
//            if (demandConfig != null && StringUtils.isNotEmpty(demandConfig.getPatentIds())) {
//                List<Map<String, Object>> patentList = patentService.getByIds(demandConfig.getPatentIds().split(","), null, new String[]{"name_vector"});
//                if (CollectionUtils.isNotEmpty(patentList)) {
//                    demand.setRecommendPatentList(patentList);
//                }
//            }
        } else {
            if (demand.getDemandSource() == Demand.FINANCE_SOURCE) {
                // 资金需求补充金融产品
                demand.setRecommendProductList(thirdFinanceService.getRecommendFinanceProductList(demand.getUserId()));
            }
        }
        // 返回companyId
        if (!Demand.NONE_FILL.equals(demand.getUserId())) {
            Map<String, Object> companyInfo = companyService.findCompanyByCreditCode(demand.getUserId());
            if (companyInfo.isEmpty()) {
                demand.setCompanyId(demand.getUserId());
            } else {
                demand.setCompanyId((String) companyInfo.get("id"));
            }
        } else {
            demand.setCompanyId(Demand.NONE_FILL);
        }
        UserInfoEntity currentUser = sysLoginService.getUserInfo(false);
        // 关联文件
        // 如果是专家，则不展示附件
        if (UserInfoEntity.USER_EXPERT != currentUser.getUserType()) {
            Map<String, List<FileInfo>> fileInfoList = fileService.listFileByRelatedIdsAndType(Arrays.asList(id), FileInfo.RELATED_TYPE_DEMAND);
            demand.setRelatedFiles(fileInfoList.get(id));
        }
        // 判断是否展示备注和评价报告性情
        boolean govView = UserInfoEntity.PLATFORM_GOV == currentUser.getPlatformType();
        if (!govView) {
            demand.setRemark(null);
        } else {
            if (DemandTypeEnum.TALENT.getName().equals(demand.getType()) || DemandTypeEnum.TECH.getName().equals(demand.getType())) {
                EvaluationQuery query = new EvaluationQuery();
                query.setContent(demand.getContent());
                query.setChainId(demand.getChainId());
                query.setChainNodeId(demand.getChainNodeId());
                Result<String> reportResult = overcomeService.demandMatch(query);
                if (reportResult.getHeader().getCode().equals(ResultConvert.SUCCESS_CODE)) {
                    demand.setReportId(reportResult.getBody());
                    if (demandConfig != null && demandConfig.customedScore()) {
                        // 放置设定的分数
                        redisCache.put("report:" + demand.getReportId(), JSONObject.toJSONString(demandConfig), 1, TimeUnit.HOURS);
                    }
                }
            }
        }
        return demand;
    }

    @Override
    public PieVO analyzeByStatus() {
        Set<String> chainIds = chainScopeService.getCurrentUserPermittedChainIds();
        List<Demand> demands = demandMapper.selectList(Wrappers.lambdaQuery(Demand.class).select(Demand::getId, Demand::getStatus)
                .in(CollectionUtils.isNotEmpty(chainIds), Demand::getChainId, chainIds));
        Map<String, Long> countMap = new HashMap<>();
        for (Demand d : demands) {
            String key = "其他";
            if (ProcedureEnum.NOT_START.getId().equals(d.getStatus())) {
                key = ProcedureEnum.NOT_START.getName();
            } else if (ProcedureEnum.END_SUCCESS.getId().compareTo(d.getStatus()) > 0
                    && ProcedureEnum.NOT_START.getId().compareTo(d.getStatus()) < 0) {
                key = "对接中";
            } else if (ProcedureEnum.END_SUCCESS.getId().equals(d.getStatus())) {
                key = ProcedureEnum.END_SUCCESS.getName();
            } else if (ProcedureEnum.END_FAIL.getId().equals(d.getStatus())) {
                key = ProcedureEnum.END_FAIL.getName();
            } else {
                key = ProcedureEnum.REJECT.getName();
            }
            countMap.computeIfAbsent(key, k -> 0L);
            countMap.put(key, countMap.get(key) + 1);
        }
        PieVO vo = new PieVO();
        vo.setTotal(demands.size());
        vo.setIndexList(CommonIndexBO.buildList(countMap));
        return vo;
    }

    @Override
    public PieVO analyzeByType() {
        Set<String> chainIds = chainScopeService.getCurrentUserPermittedChainIds();
        List<Demand> demands = demandMapper.selectList(Wrappers.lambdaQuery(Demand.class).select(Demand::getId, Demand::getType)
                .in(CollectionUtils.isNotEmpty(chainIds), Demand::getChainId, chainIds));
        Map<String, Long> countMap = demands.stream().collect(Collectors.groupingBy(Demand::getType, Collectors.counting()));
        PieVO vo = new PieVO();
        vo.setTotal(demands.size());
        vo.setIndexList(CommonIndexBO.buildList(countMap));
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submit(Demand demand) {
        if (StringUtils.isEmpty(demand.getId())) {
            demand.setId(UUID.randomUUID().toString());
        }
        demand.setStatus(ProcedureEnum.NOT_START.getId());
        if (StringUtils.isEmpty(demand.getUserId()) || StringUtils.isEmpty(demand.getUserName())) {
            UserInfoEntity userInfo = sysLoginService.getUserInfo(false);
            demand.setUserId(userInfo.getSocialCreditCode());
            demand.setUserName(userInfo.getUsername());
        }
        if (Demand.NONE_FILL.equals(demand.getChainId())) {
            demand.setChainName("其他");
        } else {
            demand.setChainName(chainService.getChainNameById(demand.getChainId()));
        }
        // 保存文件关联关系
        if (CollectionUtils.isNotEmpty(demand.getRelatedFiles())) {
            // 判断文件是否存在
            List<String> fileIds = demand.getRelatedFiles().stream().map(FileInfo::getFileId).collect(Collectors.toList());
            Long existedNum = fileService.count(Wrappers.lambdaQuery(FileInfo.class).in(FileInfo::getFileId, fileIds));
            if (existedNum != fileIds.stream().count()) {
                return false;
            }
            fileService.updateRelation(demand.getId(), FileInfo.RELATED_TYPE_DEMAND,
                    demand.getRelatedFiles().stream().map(FileInfo::getFileId).collect(Collectors.toList()));
        }
        // 加密手机号
        demand.setLink(EncryptUtil.encryptSM4FixIndex(demand.getLink(), null));
        demand.setEncryptIndex(ConfigBean.getIntVal("kind.common.encrypt.keyIndex"));
        demandMapper.insert(demand);
        return true;
    }

    @Override
    public boolean edit(Demand demand) {
        Demand existed = findExistedDemand(demand.getId());
        if (existed == null) {
            throw new BusinessException("当前需求已被删除，请刷新列表");
        }
        // 需求还未受理时，允许需求修改
        if (ProcedureEnum.NOT_START.getId().equals(existed.getStatus())) {
            if (StringUtils.isNotEmpty(demand.getContent())) {
                // 判断内容是否发生变化
                if (!demand.getContent().equals(existed.getContent())) {
                    // 重置专家，重新进行匹配
                    demandConfigService.resetExpertByDemandId(demand.getId());
                }
                existed.setContent(demand.getContent());
            }
            if (StringUtils.isNotEmpty(demand.getTitle())) {
                existed.setTitle(demand.getTitle());
            }
            if (StringUtils.isNotEmpty(demand.getLink())) {
                existed.setLink(EncryptUtil.encryptSM4FixIndex(existed.getLink(), null));
                existed.setEncryptIndex(ConfigBean.getIntVal("kind.common.encrypt.keyIndex"));
            }
            if (StringUtils.isNotEmpty(demand.getApplyAmount())) {
                existed.setApplyAmount(demand.getApplyAmount());
            }
            if (StringUtils.isNotEmpty(demand.getFinanceType())) {
                existed.setFinanceType(demand.getFinanceType());
            }
            if (StringUtils.isNotEmpty(demand.getFundPurpose())) {
                existed.setFundPurpose(demand.getFundPurpose());
            }
        }
        // 进入需求分类阶段之后，不允许修改产业链和类型
        if (ProcedureEnum.CLASSIFY.getId().compareTo(existed.getStatus()) > 0) {
            if (StringUtils.isNotEmpty(demand.getChainId())) {
                existed.setChainId(demand.getChainId());
                if (Demand.NONE_FILL.equals(demand.getChainId())) {
                    existed.setChainName("其他");
                } else {
                    existed.setChainName(chainService.getChainNameById(demand.getChainId()));
                }

            }
            if (StringUtils.isNotEmpty(demand.getChainNodeId()) && StringUtils.isNotEmpty(demand.getChainNodeName())) {
                existed.setChainNodeId(demand.getChainNodeId());
                existed.setChainNodeName(demand.getChainNodeName());
            }
            if (StringUtils.isNotEmpty(demand.getType())) {
                existed.setType(demand.getType());
            }
        }
        if (StringUtils.isNotEmpty(demand.getRemark())) {
            existed.setRemark(demand.getRemark());
        }
        demandMapper.updateById(existed);
        return true;
    }

    @Override
    public boolean accept(DemandProcess process) {
        process.setProcedureType(ProcedureEnum.START.getId());
        process.setProcedureTypeName(ProcedureEnum.START.getName());
        return follow(process);
    }

    @Override
    public boolean reject(Demand demand) {
        Demand existed = findExistedDemand(demand.getId());
        if (ProcedureEnum.REJECT.getId().equals(existed.getStatus())) {
            throw new BusinessException("需求已被退回,请刷新列表");
        }
        if (ProcedureEnum.NOT_START.getId().equals(existed.getStatus())) {
            existed.setStatus(ProcedureEnum.REJECT.getId());
            return demandMapper.updateById(existed) > 0;
        } else {
            throw new BusinessException("需求已受理，不可退回");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean follow(DemandProcess process) {
        Demand existedDemand = findExistedDemand(process.getDemandId());
        // 判断状态更换是否合理
        if (existedDemand.getStatus().compareTo(process.getProcedureType()) > 0) {
            throw new BusinessException("工作状态选择异常");
        }
        // 更新需求最新状态
        ProcedureEnum flag = ProcedureEnum.getFlagStatus(process.getProcedureType());
        existedDemand.setStatus(flag.getId());
        // 如果已催办状态，更新催办状态
        existedDemand.setUrgentStatus(0);
        demandMapper.updateById(existedDemand);
        // 如果进入到需求分类，则同步入ES
        if (ProcedureEnum.CLASSIFY.getId().equals(process.getProcedureType()) && !Demand.NONE_FILL.equals(existedDemand.getChainId())) {
            List<IndustryChainNode> pathNodes = chainService.getPathNode(existedDemand.getChainNodeId(), existedDemand.getChainId());
            elasticsearchHelper.index(EsIndexEnum.DEMAND.getEsIndex(), existedDemand.getId(),
                    Demand.toEsObject(existedDemand, pathNodes));
        }
        // 保存工作进展
        String processId = UUID.randomUUID().toString();
        process.setProcedureTypeName(ProcedureEnum.getNameById(process.getProcedureType()));
        process.setId(processId);
        setReportUserInfo(process);
        processMapper.insert(process);
        // 保存文件关联关系
        if (CollectionUtils.isNotEmpty(process.getRelatedFiles())) {
            fileService.updateRelation(processId, FileInfo.RELATED_TYPE_DEMAND_PROCESS,
                    process.getRelatedFiles().stream().map(FileInfo::getFileId).collect(Collectors.toList()));
        }
        // 如果资金需求-对接成功，额外保存放款信息
        if (DemandTypeEnum.FUND.getName().equals(existedDemand.getType()) && ProcedureEnum.END_SUCCESS.getId().equals(existedDemand.getStatus())) {
            FinanceLoanDetail loanDetail = process.getLoanDetail();
            if (loanDetail == null) {
                return true;
            }
            loanDetail.setDemandProcessId(processId);
            loanDetail.setChainId(existedDemand.getChainId());
            IndustryChainNode chainNode = chainService.getNodeById(existedDemand.getChainNodeId());
            String[] nodeIds = chainNode.getPath().split("\\|");
            String firstLevelNodeId = nodeIds.length > 1 ? nodeIds[1] : nodeIds[0];
            loanDetail.setChainNodeId(firstLevelNodeId);
            loanDetail.setChainNodeName(chainService.getNodeById(firstLevelNodeId).getName());
            loanDetail.setCreditObject(existedDemand.getUserName());
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(loanDetail.getLoanDate());
            loanDetail.setLoanYear(calendar.get(Calendar.YEAR));
            loanDetailMapper.insert(loanDetail);
        }
        return true;
    }

    private void setReportUserInfo(DemandProcess process) {
        if (StringUtils.isNotEmpty(process.getReportUserId())) {
            return;
        }
        UserInfoEntity userInfo = sysLoginService.getUserInfo(false);
        process.setReportUserId(String.valueOf(userInfo.getId()));
        process.setReportUserName(userInfo.getUsername());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean end(DemandProcess process) {
        Demand existedDemand = findExistedDemand(process.getDemandId());
        // 更新需求最新状态
        existedDemand.setStatus(ProcedureEnum.END_FAIL.getId());
        demandMapper.updateById(existedDemand);
        String processId = UUID.randomUUID().toString();
        // 保存文件关联关系
        if (CollectionUtils.isNotEmpty(process.getRelatedFiles())) {
            fileService.updateRelation(processId, FileInfo.RELATED_TYPE_DEMAND_PROCESS,
                    process.getRelatedFiles().stream().map(FileInfo::getFileId).collect(Collectors.toList()));
        }
        // 保存工作进展
        process.setId(processId);
        setReportUserInfo(process);
        process.setProcedureType(ProcedureEnum.END_FAIL.getId());
        process.setProcedureTypeName(ProcedureEnum.END_FAIL.getName());
        processMapper.insert(process);
        return true;
    }

    @Override
    public Page<DemandProcess> listProcess(ProcessPageBO processBO) {
        Page<DemandProcess> page = processMapper.selectPage(new Page<>(processBO.getPageNum(), processBO.getPageSize()),
                Wrappers.lambdaQuery(DemandProcess.class)
                        .eq(DemandProcess::getDemandId, processBO.getDemandId())
                        .ge(processBO.getCommitDateFrom() != null, DemandProcess::getCreateTime, processBO.getCommitDateFrom())
                        .le(processBO.getCommitDateTo() != null, DemandProcess::getCreateTime, processBO.getCommitDateTo())
                        .like(StringUtils.isNotEmpty(processBO.getReportUserName()), DemandProcess::getReportUserName, processBO.getReportUserName())
                        .orderByDesc(DemandProcess::getCreateTime));
        List<DemandProcess> processList = page.getRecords();
        if (CollectionUtils.isEmpty(processList)) {
            return page;
        }
        // 关联文件
        List<String> processIds = processList.stream().map(DemandProcess::getId).collect(Collectors.toList());
        Map<String, List<FileInfo>> fileInfoList = fileService.listFileByRelatedIdsAndType(processIds, FileInfo.RELATED_TYPE_DEMAND_PROCESS);
        FinanceLoanDetail loanDetail = getLoanDetailByProcessIds(processIds);
        // 关联放款信息
        for (DemandProcess process : processList) {
            process.setRelatedFiles(fileInfoList.get(process.getId()));
            if (loanDetail != null && loanDetail.getDemandProcessId().equals(process.getId())) {
                process.setLoanDetail(loanDetail);
            }
        }
        return page;
    }

    /**
     * 获取贷款信息
     *
     * @param processIds
     * @return
     */
    private FinanceLoanDetail getLoanDetailByProcessIds(List<String> processIds) {
        return loanDetailMapper.selectOne(Wrappers.lambdaQuery(FinanceLoanDetail.class)
                .in(FinanceLoanDetail::getDemandProcessId, processIds));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean evaluate(DemandEvaluation evaluation) {
        // 评价是否重复
        DemandEvaluation existed = findEvaluationByDemandIdAndType(evaluation.getDemandId(), evaluation.getType());
        if (existed != null) {
            throw new BusinessException("请勿重复评价");
        }
        // 更新需求得分
        Demand demand = findExistedDemand(evaluation.getDemandId());
        // demand.setStatus(ProcedureEnum.EVALUATE.getId());
        if (DemandEvaluation.TYPE_SERVICE.equals(evaluation.getType())) {
            demand.setServiceEvaluation(evaluation.getScore());
        } else {
            demand.setOutcomeEvaluation(evaluation.getScore());
        }
        demandMapper.updateById(demand);
        String evaluationId = UUID.randomUUID().toString();
        // 更新文件关联
        if (CollectionUtils.isNotEmpty(evaluation.getRelatedFiles())) {
            fileService.updateRelation(evaluationId, FileInfo.RELATED_TYPE_DEMAND_EVALUATION,
                    evaluation.getRelatedFiles().stream().map(FileInfo::getFileId).collect(Collectors.toList()));
        }
        evaluation.setId(evaluationId);
        demandEvaluationMapper.insert(evaluation);
        return true;
    }

    @Override
    public List<DemandEvaluation> listEvaluationByDemandId(String id) {
        List<DemandEvaluation> evaluations = demandEvaluationMapper.selectList(Wrappers.lambdaQuery(DemandEvaluation.class)
                .eq(DemandEvaluation::getDemandId, id)
                .orderByAsc(DemandEvaluation::getCreateTime));
        // 查找关联文件
        Map<String, List<FileInfo>> fileGroup = fileService.listFileByRelatedIdsAndType(evaluations.stream().map(DemandEvaluation::getId).collect(Collectors.toList()),
                FileInfo.RELATED_TYPE_DEMAND_EVALUATION);
        for (DemandEvaluation evaluation : evaluations) {
            evaluation.setRelatedFiles(fileGroup.get(evaluation.getId()));
        }
        return evaluations;
    }

    @Override
    public boolean urging(String id) {
        Demand existedDemand = findExistedDemand(id);
        existedDemand.setUrgentStatus(1);
        demandMapper.updateById(existedDemand);
        return true;
    }

    @Override
    public Page<Demand> page4Portal(DemandBO bo) {
        Page<Demand> pageResult = demandMapper.selectPage(new Page<>(bo.getPageNum(), bo.getPageSize()), Wrappers.lambdaQuery(Demand.class)
                .select(Demand::getId, Demand::getCreateTime, Demand::getChainName,
                        Demand::getChainNodeName, Demand::getType, Demand::getTitle, Demand::getContent)
                .in(Demand::getType, Arrays.asList(DemandTypeEnum.TALENT.getName(), DemandTypeEnum.TECH.getName()))
                .ge(Demand::getStatus, ProcedureEnum.CLASSIFY.getId())
                .lt(Demand::getStatus, ProcedureEnum.MATCH.getId())
                .like(StringUtils.isNotEmpty(bo.getContent()), Demand::getContent, bo.getContent())
                .le(StringUtils.isNotEmpty(bo.getCommitDateTo()), Demand::getCreateTime, bo.getCommitDateTo() + " 23:59:59")
                .ge(StringUtils.isNotEmpty(bo.getCommitDateFrom()), Demand::getCreateTime, bo.getCommitDateFrom())
                .orderByDesc(Demand::getCreateTime));
        return pageResult;
    }

    @Override
    public Page<Demand> page4ExpertPortal(DemandBO bo) {
        return page4Portal(bo);
    }

    @Override
    public boolean selfRecommend(String id) {
        UserInfoEntity currentUser = sysLoginService.getUserInfo(true);
        if (currentUser.getUserType() != UserInfoEntity.USER_EXPERT) {
            throw new BusinessException("暂不支持非专家用户揭榜");
        }
        DemandUndertake existed = undertakeMapper.selectOne(Wrappers.lambdaQuery(DemandUndertake.class)
                .eq(DemandUndertake::getUserId, currentUser.getId())
                .eq(DemandUndertake::getDemandId, id));
        if (existed != null) {
            throw new BusinessException("已揭榜，请等待平台联系");
        }
        DemandUndertake undertake = new DemandUndertake();
        undertake.setId(UUID.randomUUID().toString());
        undertake.setUserId(currentUser.getId());
        undertake.setUserName(currentUser.getAccount());
        undertake.setEncryptIndex(ConfigBean.getIntVal("kind.common.encrypt.keyIndex"));
        undertake.setLink(EncryptUtil.encryptSM4FixIndex(currentUser.getPhone(), undertake.getEncryptIndex()));
        undertake.setDemandId(id);
        undertakeMapper.insert(undertake);
        return true;
    }

    @Override
    public List<Demand> listByType(String chainId, String type, int minStatus) {
        return demandMapper.selectList(Wrappers.lambdaQuery(Demand.class)
                .eq(Demand::getChainId, chainId)
                .eq(Demand::getType, type)
                .ge(Demand::getStatus, minStatus)
                .orderByDesc(Demand::getCreateTime));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String id) {
        Demand demand = demandMapper.selectById(id);
        List<DemandProcess> processList = processMapper.selectList(Wrappers.lambdaQuery(DemandProcess.class)
                .eq(DemandProcess::getDemandId, id));
        if (CollectionUtils.isNotEmpty(processList)) {
            List<String> processIds = processList.stream().map(DemandProcess::getId).collect(Collectors.toList());
            fileService.deleteByRelatedIds(processIds);
            // 删除资金需求对接成功产生的贷款信息
            FinanceLoanDetail loanDetail = getLoanDetailByProcessIds(processIds);
            if (loanDetail != null) {
                loanDetailMapper.deleteById(loanDetail.getId());
            }
            processMapper.deleteBatchIds(processIds);
        }
        List<DemandEvaluation> evaluations = demandEvaluationMapper.selectList(Wrappers.lambdaQuery(DemandEvaluation.class)
                .eq(DemandEvaluation::getDemandId, id));
        if (CollectionUtils.isNotEmpty(evaluations)) {
            List<String> evaluationIds = evaluations.stream().map(DemandEvaluation::getId).collect(Collectors.toList());
            fileService.deleteByRelatedIds(evaluationIds);
            demandEvaluationMapper.deleteBatchIds(evaluationIds);
        }
        fileService.deleteByRelatedIds(Arrays.asList(demand.getId()));
        // 如果需求已分类，则删除es库中同步对象
        if (demand.getStatus() >= ProcedureEnum.CLASSIFY.getId()) {
            elasticsearchHelper.delete(EsIndexEnum.DEMAND.getEsIndex(), demand.getId());
        }
        // 删除需求配置
        demandConfigService.deleteByDemandId(id);
        demandMapper.deleteById(demand);
        return true;
    }

    /**
     * 按需求id和评价类型查找评价
     *
     * @param demandId
     * @param type
     * @return
     */
    private DemandEvaluation findEvaluationByDemandIdAndType(String demandId, Integer type) {
        return demandEvaluationMapper.selectOne(Wrappers.lambdaQuery(DemandEvaluation.class)
                .eq(DemandEvaluation::getDemandId, demandId)
                .eq(DemandEvaluation::getId, type));
    }

    /**
     * 查询已存在的需求
     *
     * @param id
     * @return
     */
    @Override
    public Demand findExistedDemand(String id) {
        if (StringUtils.isEmpty(id)) {
            throw new BusinessException("编辑失败，缺少必要信息");
        }
        return demandMapper.selectById(id);
    }

}
