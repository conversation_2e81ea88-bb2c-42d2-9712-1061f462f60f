package com.quantchi.nanping.innovation.knowledge.center.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quantchi.nanping.innovation.knowledge.center.dao.CompanyPatentRelationMapper;
import com.quantchi.nanping.innovation.knowledge.center.dao.CompanyTechnicalRankMapper;
import com.quantchi.nanping.innovation.knowledge.center.model.entity.*;
import com.quantchi.nanping.innovation.knowledge.center.model.enums.EvaluationEnum;
import com.quantchi.nanping.innovation.knowledge.center.model.vo.CompanyTechnicalDimension;
import com.quantchi.nanping.innovation.knowledge.center.model.vo.PatentValueAnalysis;
import com.quantchi.nanping.innovation.knowledge.center.model.vo.PatentValueDistribution;
import com.quantchi.nanping.innovation.knowledge.center.model.vo.StInnovationLevel;
import com.quantchi.nanping.innovation.knowledge.center.service.ICompanyTechnicalService;
import com.quantchi.nanping.innovation.knowledge.center.service.IEs8Service;
import com.quantchi.nanping.innovation.knowledge.center.service.IPatentValueService;
import com.quantchi.nanping.innovation.model.constant.CommonConstant;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.EsPageResult;
import com.quantchi.tianying.utils.ElasticsearchBuilder;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/19 11:06
 */
@Service
public class CompanyTechnicalServiceImpl extends ServiceImpl<CompanyPatentRelationMapper, CompanyPatentRelation> implements ICompanyTechnicalService {

    @Autowired
    private CompanyTechnicalRankMapper rankMapper;

    @Autowired
    private IPatentValueService patentValueService;

    @Autowired
    private IEs8Service es8Service;

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @Override
    public StInnovationLevel getOverviewById(String companyId, String nodeId) {
        StInnovationLevel stInnovationLevel = new StInnovationLevel();
        CompanyTechnicalInfo ranking = rankMapper.selectById(companyId);
        if (ranking == null) {
            return null;
        }
        // 计算全国范围内的分数排名
        Long excessNum = rankMapper.selectCount(Wrappers.lambdaQuery(CompanyTechnicalInfo.class)
                .lt(CompanyTechnicalInfo::getEvaluationScore, ranking.getEvaluationScore()));
        Long totalNum = rankMapper.selectCount(Wrappers.lambdaQuery(CompanyTechnicalInfo.class));
        stInnovationLevel.setEvaluationScore(ranking.getEvaluationScore());
        stInnovationLevel.setOverallEvaluation(EvaluationEnum.getEvaluationByPercentage((excessNum + 1) / (double) totalNum * 100.0).getDesc());
        stInnovationLevel.setRanking(ranking);

        List<String> nationCompanyIds = listCompanyIdByNodeIdAndScale(nodeId, true);
        List<CompanyTechnicalInfo> nationRanks = rankMapper.selectList(Wrappers.lambdaQuery(CompanyTechnicalInfo.class)
                .in(CompanyTechnicalInfo::getId, nationCompanyIds));
        List<String> provinceCompanyIds = listCompanyIdByNodeIdAndScale(nodeId, false);
        List<CompanyTechnicalInfo> provinceRanks = rankMapper.selectList(Wrappers.lambdaQuery(CompanyTechnicalInfo.class)
                .in(CompanyTechnicalInfo::getId, provinceCompanyIds));
        // 计算全国/省范围内同节点总分排名
        int nationRankNum = 0, provinceRankNum = 0;
        for (CompanyTechnicalInfo r : nationRanks) {
            if (r.getEvaluationScore() < ranking.getEvaluationScore()) {
                nationRankNum++;
            }
        }
        for (CompanyTechnicalInfo r : provinceRanks) {
            if (r.getEvaluationScore() < ranking.getEvaluationScore()) {
                provinceRankNum++;
            }
        }
        ranking.setNationalPercentile((nationRankNum + 1) / (double) nationRanks.size() * 100.0);
        ranking.setProvincialPercentile((provinceRankNum + 1) / (double) provinceRanks.size() * 100.0);
        // 计算全国/省范围内同节点维度排名
        List<CompanyTechnicalDimension> dimensionList = new ArrayList<>();
        dimensionList.add(buildDimension("研发资源", ranking.getScaleValue(), nationRanks, provinceRanks));
        dimensionList.add(buildDimension("技术质量", ranking.getQualityValue(), nationRanks, provinceRanks));
        dimensionList.add(buildDimension("协同创新", ranking.getCooperateValue(), nationRanks, provinceRanks));
        dimensionList.add(buildDimension("技术成果", ranking.getAchievementValue(), nationRanks, provinceRanks));
        dimensionList.add(buildDimension("创新持续", ranking.getSustainedValue(), nationRanks, provinceRanks));
        stInnovationLevel.setDimensionDetails(dimensionList);
//        // 计算国内和省内排名较高的指标
//        double provincePercentage = 0.0, nationPercentage = 0.0;
//        String provinceTopIndex = null, nationTopIndex = null;
//        for (CompanyTechnicalDimension d : dimensionList) {
//            if (d.getNationalPercentage() > nationPercentage) {
//                nationPercentage = d.getNationalPercentage();
//                nationTopIndex = d.getDimensionName();
//            }
//            if (d.getProvincialPercentage() >= provincePercentage) {
//                provincePercentage = d.getProvincialPercentage();
//                provinceTopIndex = d.getDimensionName();
//            }
//        }
//        ranking.setTopNationalIndex(nationTopIndex);
//        ranking.setTopProvinceIndex(provinceTopIndex);
        return stInnovationLevel;
    }

    /**
     * 查询满足目标条件的企业id
     *
     * @param nodeId
     * @param isNation
     * @return
     */
    private List<String> listCompanyIdByNodeIdAndScale(String nodeId, boolean isNation) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (!isNation) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("province.id", CommonConstant.DIVISION_FUJIAN.getId()));
        }
        boolQueryBuilder.filter(QueryBuilders.termQuery("chain_node.id", nodeId));
        SearchSourceBuilder searchSourceBuilder = EsAlterUtil.buildSearchSource(boolQueryBuilder, 1, 10000, new String[]{"id"}, null, null);
        SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchSourceBuilder, EsIndexEnum.COMPANY.getEsIndex());
        List<String> companyIds = new ArrayList<>();
        for (final SearchHit searchHit : searchResponse.getHits().getHits()) {
            Map<String, Object> sourceAsMap = searchHit.getSourceAsMap();
            companyIds.add((String) sourceAsMap.get("id"));
        }
        return companyIds;
    }

    /**
     * 计算对应维度的中位数和排名百分比
     *
     * @param dimensionName
     * @param dimensionValue
     * @param nationRanks
     * @param provinceRanks
     * @return
     */
    private CompanyTechnicalDimension buildDimension(String dimensionName, Integer dimensionValue, List<CompanyTechnicalInfo> nationRanks, List<CompanyTechnicalInfo> provinceRanks) {
        List<Integer> nationDimensionValues = null, provinceDimensionValues = null;
        if ("研发资源".equals(dimensionName)) {
            nationDimensionValues = nationRanks.stream().map(CompanyTechnicalInfo::getScaleValue).collect(Collectors.toList());
            provinceDimensionValues = provinceRanks.stream().map(CompanyTechnicalInfo::getScaleValue).collect(Collectors.toList());
        } else if ("技术质量".equals(dimensionName)) {
            nationDimensionValues = nationRanks.stream().map(CompanyTechnicalInfo::getQualityValue).collect(Collectors.toList());
            provinceDimensionValues = provinceRanks.stream().map(CompanyTechnicalInfo::getQualityValue).collect(Collectors.toList());
        } else if ("协同创新".equals(dimensionName)) {
            nationDimensionValues = nationRanks.stream().map(CompanyTechnicalInfo::getCooperateValue).collect(Collectors.toList());
            provinceDimensionValues = provinceRanks.stream().map(CompanyTechnicalInfo::getCooperateValue).collect(Collectors.toList());
        } else if ("技术成果".equals(dimensionName)) {
            nationDimensionValues = nationRanks.stream().map(CompanyTechnicalInfo::getAchievementValue).collect(Collectors.toList());
            provinceDimensionValues = provinceRanks.stream().map(CompanyTechnicalInfo::getAchievementValue).collect(Collectors.toList());
        } else {
            nationDimensionValues = nationRanks.stream().map(CompanyTechnicalInfo::getSustainedValue).collect(Collectors.toList());
            provinceDimensionValues = provinceRanks.stream().map(CompanyTechnicalInfo::getSustainedValue).collect(Collectors.toList());
        }
        Pair<Double, Double> nationCompareInfo = calculateMedianAndExcessPercentage(nationDimensionValues, dimensionValue);
        Pair<Double, Double> provinceCompareInfo = calculateMedianAndExcessPercentage(provinceDimensionValues, dimensionValue);
        CompanyTechnicalDimension dimensionVO = new CompanyTechnicalDimension();
        dimensionVO.setDimensionName(dimensionName);
        dimensionVO.setEvaluationScore(dimensionValue);
        dimensionVO.setNationalMedian(nationCompareInfo.getKey());
        dimensionVO.setNationalPercentage(nationCompareInfo.getValue());
        dimensionVO.setProvincialMedian(provinceCompareInfo.getKey());
        dimensionVO.setProvincialPercentage(provinceCompareInfo.getValue());
        return dimensionVO;
    }

    /**
     * 计算中位数和超过的百分比
     *
     * @param dimensionValues
     * @return
     */
    private Pair<Double, Double> calculateMedianAndExcessPercentage(List<Integer> dimensionValues, Integer currentValue) {
        Integer[] numbers = dimensionValues.toArray(new Integer[0]);
        Arrays.sort(numbers);
        int middle = numbers.length / 2;
        double medianValue = 0.0;
        if (numbers.length % 2 == 1) {
            // 如果数组长度是奇数，直接返回中间的数字
            medianValue = numbers[middle];
        } else {
            // 如果数组长度是偶数，返回中间两个数字的平均值
            medianValue = (numbers[middle - 1] + numbers[middle]) / 2.0;
        }
        int excessNum = 0;
        for (int i = 0; i < numbers.length; i++) {
            if (numbers[i] >= currentValue) {
                excessNum = i + 1;
                break;
            }
        }
        double excessPercentage = (excessNum + 1) / (double) numbers.length * 100.0;
        return new ImmutablePair<>(medianValue, excessPercentage);
    }

    @Override
    public PatentValueAnalysis getPatentAnalysisById(String companyId) {
        PatentValueAnalysis analysis = new PatentValueAnalysis();
        analysis.setAveragePatentValueUnit("万元");
        analysis.setTotalPatentValueUnit("万元");
        PatentValueUnit unit = patentValueService.getCurrentUnit();
        List<CompanyPatentRelation> patentRelations = this.list(Wrappers.lambdaQuery(CompanyPatentRelation.class).eq(CompanyPatentRelation::getCompanyId, companyId));
        if (CollectionUtils.isEmpty(patentRelations)) {
            return null;
        }
        Map<String, CompanyPatentRelation> idRelationMap = patentRelations.stream().collect(Collectors.toMap(CompanyPatentRelation::getPatentId, Function.identity()));
        Map<String, CompanyPatentRelation> codeRelationMap = patentRelations.stream().collect(Collectors.toMap(CompanyPatentRelation::getPublicCode, Function.identity()));
        BigDecimal totalValue = BigDecimal.ZERO;
        List<PatentValue> patentValueList = patentValueService.listValidByIds(patentRelations.stream().map(CompanyPatentRelation::getPatentId).collect(Collectors.toList()));
        Map<String, PatentValue> idValueMap = patentValueList.stream().collect(Collectors.toMap(PatentValue::getId, Function.identity()));
        Map<String, BigDecimal> codeValueMap = new HashMap<>();
        for (PatentValue value : patentValueList) {
            CompanyPatentRelation relation = idRelationMap.get(value.getId());
            codeValueMap.put(relation.getPublicCode(), value.getValue());
        }
        Map<String, BigDecimal> newCodeValueMap = new HashMap<>();
        // 记录重复计算价值的简单同族专利
        Set<String> duplicateCodeSet = new HashSet<>();
        for (PatentValue value : patentValueList) {
            // 判断是否有同族专利
            CompanyPatentRelation relation = idRelationMap.get(value.getId());
            if (relation == null) {
                continue;
            }
            if (StringUtils.isEmpty(relation.getSimpleFamily())) {
                totalValue = totalValue.add(value.getValue());
                newCodeValueMap.put(relation.getPublicCode(), value.getValue());
                continue;
            }
            // 有同族专利
            List<String> familyCodes = Arrays.asList(relation.getSimpleFamily().split(";"));
            int familyCount = 0;
            BigDecimal totalFamilyValue = BigDecimal.ZERO;
            for (String familyCode : familyCodes) {
                CompanyPatentRelation familyRelation = codeRelationMap.get(familyCode);
                if (familyRelation == null) {
                    continue;
                }
                PatentValue familyValue = idValueMap.get(familyRelation.getPatentId());
                if (familyValue == null) {
                    continue;
                }
                totalFamilyValue = totalFamilyValue.add(familyValue.getValue());
                familyCount++;
            }
            BigDecimal newPatentValue = totalFamilyValue.divide(new BigDecimal(familyCount), 2, RoundingMode.HALF_UP);
            newCodeValueMap.put(relation.getPublicCode(), newPatentValue);
            duplicateCodeSet.add(relation.getPublicCode());
            // 重新对同族专利赋值
            for (String familyCode : familyCodes) {
                if (familyCode.equals(relation.getPublicCode())) {
                    continue;
                }
                newCodeValueMap.put(familyCode, new BigDecimal(-1));
                duplicateCodeSet.add(familyCode);
            }
        }
        int noneZeroNum = 0;
        for (BigDecimal v : newCodeValueMap.values()) {
            if (new BigDecimal(-1).compareTo(v) < 0) {
                totalValue = totalValue.add(v);
                noneZeroNum++;
            }
        }
        analysis.setTotalPatentValue(totalValue.multiply(unit.getExchangeRate()));
        analysis.setAveragePatentValue(totalValue.divide(new BigDecimal(noneZeroNum), 2, RoundingMode.HALF_UP).multiply(unit.getExchangeRate()));
        // 按专利价值计数
        analysis.setPatentValueDistribution(getValueDistribution(patentValueList, unit.getExchangeRate()));
        // 计算数量最多的专利类型和发明专利占比
        Map<String, List<CompanyPatentRelation>> typeCount = patentRelations.stream().filter(r -> idValueMap.containsKey(r.getPatentId())).collect(Collectors.groupingBy(CompanyPatentRelation::getPatentType));
        analysis.setInventionPatentNum(typeCount.containsKey("发明专利") ? typeCount.get("发明专利").size() : 0);
        analysis.setInventionPatentRatio(new BigDecimal(analysis.getInventionPatentNum()).divide(new BigDecimal(patentValueList.size()), 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal(100)));
        int maxTypeCount = 0;
        for (Map.Entry<String, List<CompanyPatentRelation>> type : typeCount.entrySet()) {
            if (type.getValue().size() > maxTypeCount) {
                maxTypeCount = type.getValue().size();
                analysis.setMostInventType(type.getKey());
            }
        }
        analysis.setMostInventTypePercentile(new BigDecimal(maxTypeCount).divide(new BigDecimal(patentValueList.size()), 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal(100)));
        // 展示价值排名前五的专利
        List<PatentValueAnalysis.CorePatent> patents = new ArrayList<>();
        List<String> targetPatentIds = patentValueList.subList(0, patentValueList.size() > 5 ? 5 : patentValueList.size())
                .stream().map(PatentValue::getId).collect(Collectors.toList());
        SearchSourceBuilder searchBuilder = new SearchSourceBuilder();
        BoolQueryBuilder filterBuilder = QueryBuilders.boolQuery();
        filterBuilder.filter(QueryBuilders.idsQuery().addIds(targetPatentIds.toArray(new String[0])));
        searchBuilder.size(5);
        searchBuilder.query(filterBuilder);
        searchBuilder.fetchSource(null, new String[]{"ti_vector"});
        SearchResponse searchResponse = es8Service.request(searchBuilder, EsIndexEnum.PATENT_VECTOR.getEsIndex(), null, null);
        EsPageResult esPageResult = ElasticsearchBuilder.buildPageResult(searchResponse);
        for (Map<String, Object> record : esPageResult.getList()) {
            List<Map<String, Object>> inventorList = (List<Map<String, Object>>) record.get("inventors");
            List<String> inventorNames = new ArrayList<>();
            inventorList.forEach(i -> inventorNames.add((String) i.get("name")));
            patents.add(new PatentValueAnalysis.CorePatent((String) record.get("id"), (String) record.get("title_cn"), null,
                    (String) record.get("patent_type"), (String) record.get("status"), (String) record.get("public_code"), (String) record.get("public_date"),
                    (String) record.get("main_ipc"), inventorNames));
        }
        analysis.setTop5CorePatents(patents);
        return analysis;
    }

    @Override
    public Map<String, Object> getTotalCompanyValue() {
        Map<String, Object> resultMap = new HashMap<>();
        List<CompanyPatentRelation> patentRelations = this.list(Wrappers.lambdaQuery(CompanyPatentRelation.class));
        Map<String, List<CompanyPatentRelation>> relationGroup = patentRelations.stream().collect(Collectors.groupingBy(CompanyPatentRelation::getCompanyId));
        for (Map.Entry<String, List<CompanyPatentRelation>> companyGroup : relationGroup.entrySet()) {
            List<CompanyPatentRelation> relations = companyGroup.getValue();
            String companyId = companyGroup.getKey();
            Map<String, Object> companyInfo = elasticsearchHelper.getDataById(EsIndexEnum.COMPANY.getEsIndex(), companyId, null, null);
            Map<String, CompanyPatentRelation> idRelationMap = relations.stream().collect(Collectors.toMap(CompanyPatentRelation::getPatentId, Function.identity()));
            Map<String, CompanyPatentRelation> codeRelationMap = relations.stream().collect(Collectors.toMap(CompanyPatentRelation::getPublicCode, Function.identity()));
            BigDecimal totalValue = BigDecimal.ZERO;
            List<PatentValue> patentValueList = patentValueService.listValidByIds(relations.stream().map(CompanyPatentRelation::getPatentId).collect(Collectors.toList()));
            Map<String, PatentValue> idValueMap = patentValueList.stream().collect(Collectors.toMap(PatentValue::getId, Function.identity()));
            Map<String, BigDecimal> codeValueMap = new HashMap<>();
            for (PatentValue value : patentValueList) {
                CompanyPatentRelation relation = idRelationMap.get(value.getId());
                codeValueMap.put(relation.getPublicCode(), value.getValue());
            }
            Map<String, BigDecimal> newCodeValueMap = new HashMap<>();
            // 记录重复计算价值的简单同族专利
            Set<String> duplicateCodeSet = new HashSet<>();
            for (PatentValue value : patentValueList) {
                // 判断是否有同族专利
                CompanyPatentRelation relation = idRelationMap.get(value.getId());
                if (relation == null) {
                    continue;
                }
                if (StringUtils.isEmpty(relation.getSimpleFamily())) {
                    totalValue = totalValue.add(value.getValue());
                    newCodeValueMap.put(relation.getPublicCode(), value.getValue());
                    continue;
                }
                // 有同族专利
                List<String> familyCodes = Arrays.asList(relation.getSimpleFamily().split(";"));
                int familyCount = 0;
                BigDecimal totalFamilyValue = BigDecimal.ZERO;
                for (String familyCode : familyCodes) {
                    CompanyPatentRelation familyRelation = codeRelationMap.get(familyCode);
                    if (familyRelation == null) {
                        continue;
                    }
                    PatentValue familyValue = idValueMap.get(familyRelation.getPatentId());
                    if (familyValue == null) {
                        continue;
                    }
                    totalFamilyValue = totalFamilyValue.add(familyValue.getValue());
                    familyCount++;
                }
                BigDecimal newPatentValue = totalFamilyValue.divide(new BigDecimal(familyCount), 2, RoundingMode.HALF_UP);
                newCodeValueMap.put(relation.getPublicCode(), newPatentValue);
                duplicateCodeSet.add(relation.getPublicCode());
                // 重新对同族专利赋值
                for (String familyCode : familyCodes) {
                    if (familyCode.equals(relation.getPublicCode())) {
                        continue;
                    }
                    newCodeValueMap.put(familyCode, new BigDecimal(-1));
                    duplicateCodeSet.add(familyCode);
                }
            }
            for (BigDecimal v : newCodeValueMap.values()) {
                if (new BigDecimal(-1).compareTo(v) < 0) {
                    totalValue = totalValue.add(v);
                }
            }
            resultMap.put((String) companyInfo.get("name"), totalValue.multiply(new BigDecimal(7.13)).toString());
        }
        return resultMap;
    }

    @Override
    public boolean dsiplayTechnicalEvaluation(String companyId) {
        //CompanyTechnicalInfo rank = rankMapper.selectById(companyId);
        List<CompanyPatentRelation> relations = this.list(Wrappers.lambdaQuery(CompanyPatentRelation.class).eq(CompanyPatentRelation::getCompanyId, companyId)
                .ne(CompanyPatentRelation::getPatentType, "外观设计"));
        return relations.size() > 0;
    }

    /**
     * 按专利价值范围统计数量分布
     *
     * @param patentValueList
     * @return
     */
    private PatentValueDistribution getValueDistribution(List<PatentValue> patentValueList, BigDecimal exchangeRate) {
        Long range0To10Million = 0L, range10To50Million = 0L, range50To100Million = 0L, range100To500Million = 0L, rangeAbove500Million = 0L;
        for (PatentValue value : patentValueList) {
            BigDecimal exchangeValue = value.getValue().multiply(exchangeRate);
            if (exchangeValue.compareTo(new BigDecimal(10)) < 0) {
                range0To10Million++;
            } else if (exchangeValue.compareTo(new BigDecimal(50)) < 0) {
                range10To50Million++;
            } else if (exchangeValue.compareTo(new BigDecimal(100)) < 0) {
                range50To100Million++;
            } else if (exchangeValue.compareTo(new BigDecimal(500)) < 0) {
                range100To500Million++;
            } else {
                rangeAbove500Million++;
            }
        }
        return new PatentValueDistribution(range0To10Million, range10To50Million, range50To100Million, range100To500Million, rangeAbove500Million);
    }
}
