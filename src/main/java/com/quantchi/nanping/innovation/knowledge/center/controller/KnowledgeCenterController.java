package com.quantchi.nanping.innovation.knowledge.center.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.quantchi.anti.annotation.AntiReptile;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.company.model.CompanyCollection;
import com.quantchi.nanping.innovation.company.service.ICompanyCollectionService;
import com.quantchi.nanping.innovation.company.service.ICompanyNodeRelationService;
import com.quantchi.nanping.innovation.config.aop.Log;
import com.quantchi.nanping.innovation.config.aop.Metrics;
import com.quantchi.nanping.innovation.config.aop.RateLimiter;
import com.quantchi.nanping.innovation.knowledge.center.config.EsBoostProperties;
import com.quantchi.nanping.innovation.knowledge.center.config.FieldBoostProperty;
import com.quantchi.nanping.innovation.knowledge.center.model.bo.IndustryPageBO;
import com.quantchi.nanping.innovation.knowledge.center.model.bo.ResourceInfoQO;
import com.quantchi.nanping.innovation.knowledge.center.model.enums.KnowledgeCenterEnum;
import com.quantchi.nanping.innovation.knowledge.center.service.ICompanyTechnicalService;
import com.quantchi.nanping.innovation.knowledge.center.service.IEs8Service;
import com.quantchi.nanping.innovation.knowledge.center.service.IKnowledgeCenterQueryService;
import com.quantchi.nanping.innovation.knowledge.center.service.impl.PatentEsInfoService;
import com.quantchi.nanping.innovation.knowledge.center.utils.ElasticsearchBuilder;
import com.quantchi.nanping.innovation.knowledge.center.utils.export.CompanyExportUtil;
import com.quantchi.nanping.innovation.model.IndustryChainNode;
import com.quantchi.nanping.innovation.model.TechCommissioner;
import com.quantchi.nanping.innovation.model.UserInfoEntity;
import com.quantchi.nanping.innovation.model.bo.EsSimpleQuery;
import com.quantchi.nanping.innovation.model.constant.CommonConstant;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.service.*;
import com.quantchi.nanping.innovation.service.impl.SysLoginService;
import com.quantchi.nanping.innovation.service.library.CompanyService;
import com.quantchi.nanping.innovation.service.library.PatentService;
import com.quantchi.nanping.innovation.utils.AESUtil;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.nanping.innovation.utils.StringRedisCache;
import com.quantchi.tianying.config.property.CustomIndexNavSetting;
import com.quantchi.tianying.config.property.NavigationSettings;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.EsPageResult;
import com.quantchi.tianying.model.MultidimensionalQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.functionscore.FunctionScoreQueryBuilder;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2023/1/13 1:58 下午
 * @description
 */
@Api(tags = "知识中心")
@Slf4j
@RestController
@RequestMapping("/knowledge")
@Metrics
public class KnowledgeCenterController {

    @Resource
    private EsBoostProperties esBoostProperties;

    @Resource
    private NavigationSettings navigationSettings;

    @Resource
    private ElasticsearchHelper elasticsearchHelper;

    @Autowired
    private ElasticsearchBuilder elasticsearchBuilder;

    @Autowired
    private ICompanyCollectionService collectionService;

    @Autowired
    private SysLoginService sysLoginService;

    @Autowired
    private ITechCommissionerService commissionerService;

    @Autowired
    private IndustryChainService industryChainService;

    @Autowired
    private StringRedisCache redisCache;

    @Autowired
    private ICompanyNodeRelationService companyNodeRelationService;

    @Autowired
    private ISysUserChainScopeService chainScopeService;

    @Autowired
    private IWordVectorService wordVectorService;

    @Autowired
    private IEs8Service es8Service;

    @Autowired
    private ICompanyTechnicalService companyTechnicalService;

    @Autowired
    private PatentEsInfoService patentEsInfoService;

    @Autowired
    private PatentService patentService;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private IKnowledgeCountService knowledgeCountService;

    @Autowired
    private IKnowledgeCenterQueryService knowledgeCenterQueryService;

    @ApiOperation("资源库-获取库映射")
    @PostMapping("/get_source_map")
    //@AntiReptile
    //@Log(title = "知识中心-库映射")
    public Result get_source_map(boolean isService) {
        JSONArray res = new JSONArray();
        List<String> serviceIndexTypes = Arrays.asList("policy", "news", "financing", "company");
        boolean permissionControl = false;
        Map<String, Boolean> permittedMenuMap = new HashMap<>();
        if (StpUtil.isLogin()) {
            // 该用户为政府侧，计算有权限的菜单范围
            UserInfoEntity userInfo = sysLoginService.getUserInfo(false);
            if (UserInfoEntity.PLATFORM_GOV == userInfo.getPlatformType() && !userInfo.isAdmin()) {
                permissionControl = true;
                permittedMenuMap = sysLoginService.getPermittedMenus(userInfo.getId(), false);
            }
        }
        for (KnowledgeCenterEnum value : KnowledgeCenterEnum.values()) {
            // 菜单控制
            if (permissionControl && !permittedMenuMap.get(value.getEsIndex())) {
                continue;
            }
            // 服务侧展示政策/资讯/投融资/企业
            if (isService && !serviceIndexTypes.contains(value.getType())) {
                continue;
            }
            JSONObject temp = new JSONObject();
            temp.put("index", value.getEsIndex());
            temp.put("disabled", value.getDisabled());
            temp.put("name", value.getIndexName());
            res.add(temp);
        }
        return ResultConvert.success(res);
    }

    @ApiOperation("资源库-获取节点筛选列表")
    @GetMapping("/getNodeSetting")
    //@AntiReptile
    public Result getNodeSetting() {
        List<IndustryChainNode> nodeList = industryChainService.getNodeListByMaxLevel(3);
        List<IndustryChainNode> rootList = new ArrayList<>();
        Map<String, IndustryChainNode> rootMap = new HashMap<>();
        final String firstGroup = "first", secondGroup = "second";
        Set<String> chainIds = chainScopeService.getCurrentUserPermittedChainIds();
        for (IndustryChainNode node : nodeList) {
            if (CollectionUtils.isNotEmpty(chainIds) && !chainIds.contains(node.getChainId())) {
                continue;
            }
            if (StringUtils.isNotEmpty(node.getParentId())) {
                String rootId = node.getPath().split("\\|")[0];
                IndustryChainNode rootNode = rootMap.get(rootId);
                ((List) rootNode.getProperties().get(node.getLevel() == 2 ? firstGroup : secondGroup)).add(node);
            } else {
                Map<String, Object> propertyMap = new LinkedHashMap<>();
                propertyMap.put(firstGroup, new ArrayList<>());
                propertyMap.put(secondGroup, new ArrayList<>());
                node.setProperties(propertyMap);
                rootMap.put(node.getId(), node);
                rootList.add(node);
            }
        }
        return ResultConvert.success(rootList);
    }
//
//    @ApiOperation("资源库-获取节点筛选列表")
//    @GetMapping("/getNodeList")
//    public Result<List<IndustryChainNode>> getNodeList(String chainId, String parentNodeId) {
//        if (StringUtils.isEmpty(chainId) || StringUtils.isEmpty(parentNodeId)){
//            return ResultConvert.success(industryChainService.listRoots());
//        }
//        return ResultConvert.success(industryChainService.listByChainIdAndParentNodeId(chainId, parentNodeId));
//    }

    /**
     * 获取对应索引条目的导航栏设置
     *
     * @param index
     * @return
     */
    @ApiOperation("资源库-获取对应索引条目的导航栏设置")
    @GetMapping("/getNavSetting")
    //@AntiReptile
    //@Log(title = "知识中心-导航栏设置")
    public Result getNavSetting(@NonNull String index) {
        index += "pre".equals(SpringUtil.getActiveProfile()) ? "_dev" : com.quantchi.common.core.utils.StringUtils.EMPTY;
        List<CustomIndexNavSetting> indexSettings = navigationSettings.getIndexNavSettingMap().get(index);
        List<CustomIndexNavSetting> resultSettings = new LinkedList<>();
        indexSettings.forEach(e -> {
            if (CollectionUtils.isNotEmpty(e.getScope())) {
                resultSettings.add(e);
            }
        });
        return ResultConvert.success(resultSettings);
    }

    @ApiOperation("服务侧看产业/查人才")
    @PostMapping("/recommend_page")
    @AntiReptile
    @Log(title = "看产业/查人才")
    public Result page4RelatedNode(@RequestBody IndustryPageBO pageBO) {
        if (StringUtils.isEmpty(pageBO.getIndex())) {
            return ResultConvert.error(ResultConvert.ERROR_CODE, "缺少查询主体");
        }
        EsIndexEnum esIndexEnum = EsIndexEnum.getEsIndexByIndex(pageBO.getIndex());
        if (esIndexEnum == null) {
            return ResultConvert.error(ResultConvert.ERROR_CODE, "查询主体不存在");
        }
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(pageBO.getKeyword())) {
            List<FieldBoostProperty> boostList = esBoostProperties.getFieldBoostListByIndex(pageBO.getIndex());
            ElasticsearchBuilder.keywordQueryWithOperator(boolQueryBuilder, pageBO.getKeyword(),
                    Operator.AND, boostList);
        }
        if (StpUtil.isLogin() && org.apache.commons.lang3.StringUtils.isNotEmpty(pageBO.getEntityId())) {
            // 查询用户所在的链和节点
            Pair<Set<String>, Set<String>> chainInfo = companyNodeRelationService.getRelatedNodeIdsByEntityId(pageBO.getEntityId(),
                    UserInfoEntity.USER_COMPANY == pageBO.getUserType(), UserInfoEntity.USER_EXPERT == pageBO.getUserType());
            Set<String> chainIds = chainInfo.getLeft();
            if (CollectionUtils.isNotEmpty(chainIds)) {
                boolQueryBuilder.filter(QueryBuilders.termsQuery("chain.id", chainIds));
            }
        }
        if (EsIndexEnum.POLICY.getEsIndex().equals(esIndexEnum.getEsIndex())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("province", CommonConstant.DIVISION_FUJIAN.getName()));
            boolQueryBuilder.filter(QueryBuilders.existsQuery("chain"));
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("chain", ""));
            boolQueryBuilder.filter(QueryBuilders.termQuery("is_valid", 1));
        }
        String sortField = esIndexEnum.getSort();
        List<FunctionScoreQueryBuilder.FilterFunctionBuilder> customSortRules = EsAlterUtil.buildCustomSortRule(pageBO.getIndex(), null);
        // 获取结果
        final SearchResponse searchResponse = elasticsearchBuilder.pageByFields(pageBO.getIndex(), pageBO.getKeyword(), boolQueryBuilder,
                pageBO.getPageNum(), pageBO.getPageSize(), sortField, customSortRules);
        EsPageResult resultMap = com.quantchi.tianying.utils.ElasticsearchBuilder.buildPageResult(searchResponse);
        if (StpUtil.isLogin()) {
            // 设置收藏标志
            List<CompanyCollection> collections = collectionService.listByType(sysLoginService.findCollectEntityId(), esIndexEnum.getType());
            List<String> collectedIds = collections.stream().map(CompanyCollection::getCollectId).collect(Collectors.toList());
            resultMap.getList().forEach(r -> {
                if (collectedIds.contains((String) r.get("id"))) {
                    r.put("collected", true);
                }
            });
        }
        return ResultConvert.success(resultMap);
    }

    /**
     * 导航菜单筛选+关键字查询
     *
     * @param mQuery
     * @return
     */
    @ApiOperation("资源库-多维导航搜索")
    @PostMapping("/filtering")
    @RateLimiter(key = "rate_limit:knowledge_filter:", count = 60)
    @Log(title = "知识中心")
    public Result queryByTermsAndKey(@NonNull @RequestBody MultidimensionalQuery mQuery, boolean isService, String nodePath) {
        try {
            EsPageResult resultMap = knowledgeCenterQueryService.queryByTermsAndKey(mQuery, isService, nodePath);
            return ResultConvert.success(resultMap);
        } catch (IllegalArgumentException e) {
            return ResultConvert.error(ResultConvert.ERROR_CODE, e.getMessage());
        } catch (Exception e) {
            log.error("查询失败", e);
            return ResultConvert.error(ResultConvert.ERROR_CODE, "查询失败");
        }
    }

    @ApiOperation("详情")
    @GetMapping("/detail")
    @RateLimiter(key = "rate_limit:knowledge_datail:", count = 60)
    @Log(title = "知识中心-详情")
    public Result detail(ResourceInfoQO infoQO, boolean isService, boolean chat, HttpServletRequest request) {
        infoQO.setIndex(infoQO.getIndex() + ("pre".equals(SpringUtil.getActiveProfile()) ? "_dev" : com.quantchi.common.core.utils.StringUtils.EMPTY));
        EsIndexEnum selectIndex = EsIndexEnum.getEsIndexByIndex(infoQO.getIndex());
        // 人才详情，仅治理侧展示联系方式
        String[] excludes = new String[]{"tel", "name_vector", "desc_research_vector"};
        if (StpUtil.isLogin()) {
            UserInfoEntity infoEntity = sysLoginService.getUserInfo(false);
            if (UserInfoEntity.PLATFORM_GOV == infoEntity.getPlatformType()) {
                excludes = null;
            }
        }
        Map<String, Object> source = null;
        if (infoQO.getIndex().equals(EsIndexEnum.PATENT.getEsIndex())){
            source = es8Service.getDataById(EsIndexEnum.PATENT.getEsIndex(), infoQO.getId(), null, excludes);
        }else{
            source = elasticsearchHelper.getDataById(selectIndex.getEsIndex(), infoQO.getId(), null, excludes);
        }
        if (null == source) {
            return ResultConvert.success();
        }
        //企业库查询融资信息、专利信息、软著、产品信息
        if (infoQO.getIndex().equals(EsIndexEnum.COMPANY.getEsIndex())) {
            //企业库
            String companyId = String.valueOf(source.get("id"));
            //融资
            source.put(EsIndexEnum.FINANCING.getType(), buildSourceMap("financing_company.id", companyId, EsIndexEnum.FINANCING));
            //专利
            //source.put(EsIndexEnum.PATENT.getType(), patentService.pageByCompanyId(companyId, 1, 10000));
            //资讯
            source.put(EsIndexEnum.NEWS.getType(), buildSourceMap("entities.id", companyId, EsIndexEnum.NEWS));
            // 计算专利价值一级节点领域（除根节点外的一级节点）
            List<Map<String, Object>> firstNodeList = new ArrayList<>();
            List<Map<String, Object>> chainNodeList = (List<Map<String, Object>>) source.get("chain_node");
            if (CollectionUtils.isNotEmpty(chainNodeList)) {
                for (Map<String, Object> chainNode : chainNodeList) {
                    String nodeId = (String) chainNode.get("id");
                    String[] nodePartArray = nodeId.split("-");
                    if (nodePartArray.length <= 1) {
                        continue;
                    }
                    String nodePartNo = nodePartArray[1];
                    if (nodePartNo.length() == 2) {
                        firstNodeList.add(chainNode);
                    }
                }
                source.put("firstNode", firstNodeList);
            }
            // 判断是否有科创实力评价
            source.put("haveStStrength", companyTechnicalService.dsiplayTechnicalEvaluation(infoQO.getId()));
            // 排序
            CompanyExportUtil.sortBySizeDesc((List<Map<String, Object>>) source.get("shareholder"), "stock_amount");
            // 补充信息
            Map<String, Object> metaInfo = elasticsearchHelper.getDataById(EsIndexEnum.COMPANY_META.getEsIndex(), infoQO.getId(), null, null);
            if (metaInfo != null){
                CompanyExportUtil.sortByDateDesc((List<Map<String, Object>>) metaInfo.get("invest"), "invested_established_date");
                CompanyExportUtil.sortByDateDesc((List<Map<String, Object>>) metaInfo.get("change"), "change_date");
                CompanyExportUtil.sortByDateDesc((List<Map<String, Object>>) metaInfo.get("abnormal"), "date_in");
                CompanyExportUtil.sortByDateDesc((List<Map<String, Object>>) metaInfo.get("punish"), "decision_date");
                CompanyExportUtil.sortByDateDesc((List<Map<String, Object>>) metaInfo.get("illegal"), "date_in");
                CompanyExportUtil.sortByDateDesc((List<Map<String, Object>>) metaInfo.get("judgement"), "judgment_date");
            }
            source.put("meta", metaInfo);
            source.put("stock", companyService.getAnnualReport(infoQO.getId()));
        } else if (infoQO.getIndex().equals(EsIndexEnum.EXPERT.getEsIndex())) {
            //人才库
            //补充chain_node里chain_name信息
            List<Map<String, Object>> productNodeList = (List<Map<String, Object>>) source.get("product_node");
            if (CollectionUtils.isNotEmpty(productNodeList)) {
                List<Map<String, Object>> chainList = (List<Map<String, Object>>) source.get("chain");
                Map<String, String> chainIdNameMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(chainList)) {
                    for (Map<String, Object> chain : chainList) {
                        chainIdNameMap.put((String) chain.get("id"), (String) chain.get("name"));
                    }
                }
                productNodeList.forEach(e -> e.put("chain_name", chainIdNameMap.get((String) e.get("chain_id"))));
            }
            // 如果是科特派，补充服务经历
            if (source.containsKey("source")
                    && (((List<Object>) source.get("source")).contains(2) || ((List<Object>) source.get("source")).contains(3))) {
                List<TechCommissioner> serviceList = commissionerService.listExperienceByDataId(infoQO.getId());
                source.put("service", serviceList);
                source.put("commissionerType", serviceList.stream().map(TechCommissioner::getLevel).collect(Collectors.toSet()));
            }
            // 对联系方式和邮箱进行解密
            source.put("tel", AESUtil.decrypt((String) source.get("tel")));
            source.put("email", AESUtil.decrypt((String) source.get("email")));
        } else if (infoQO.getIndex().equals(EsIndexEnum.FINANCING.getEsIndex())) {
            // 融资库
            // 补充公司简介
            String companyId = String.valueOf(((Map<String, Object>) source.get("financing_company")).get("id"));
            Map<String, Object> companyInfo = elasticsearchHelper.getDataById(EsIndexEnum.COMPANY.getEsIndex(), companyId, new String[]{"desc"}, null);
            source.put("desc", companyInfo.get("desc"));
            // 补充融资历程
            source.put("progress", buildSourceMap("financing_company.id", companyId, EsIndexEnum.FINANCING));
        } else if (infoQO.getIndex().equals(EsIndexEnum.ACHIEVEMENT.getEsIndex())) {
            // 成果库，有项目信息的补充信息
            if (source.containsKey("project")) {
                String projectId = String.valueOf(((Map<String, Object>) source.get("project")).get("id"));
                if (StringUtils.isNotEmpty(projectId)) {
                    Map<String, Object> projectInfo = elasticsearchHelper.getDataById(EsIndexEnum.PROJECT.getEsIndex(), projectId, null, null);
                    source.remove("project");
                    source.put("project", projectInfo);
                }
            } else if (source.containsKey("type") && "专利".equals(((List<String>) source.get("type")).get(0))) {
                // 成果库，有专利信息补充专利信息
                Map<String, Object> patentInfo = elasticsearchHelper.getDataById(EsIndexEnum.PATENT.getEsIndex(), infoQO.getId(), null, null);
                source.put("patent", patentInfo);
            }
        }else if (EsIndexEnum.PATENT.getEsIndex().equals(infoQO.getIndex())){
            if (StringUtils.isNotEmpty((String)source.get("abstract_cn"))){
                source.put("abstract", source.get("abstract_cn"));
            }
            if (StringUtils.isNotEmpty((String)source.get("title_cn"))){
                source.put("name", source.get("title_cn"));
            }
        }
        if (isService && StpUtil.isLogin()) {
            // 设置收藏标志
            List<CompanyCollection> collections = collectionService.listByType(sysLoginService.findCollectEntityId(), selectIndex.getType());
            List<String> collectedIds = collections.stream().map(CompanyCollection::getCollectId).collect(Collectors.toList());
            if (collectedIds.contains(source.get("id"))) {
                source.put("collected", true);
            }
        }
        return ResultConvert.success(source);
    }

    @ApiOperation("同领域下的要素列表")
    @GetMapping("/similar")
    @RateLimiter(key = "rate_limit:knowledge_similar:", count = 60)
    public Result getOtherResourceInSameDomain(@NotBlank String index, @NotBlank String id, @RequestParam(defaultValue = "1") Integer pageNum,
                                               @RequestParam(defaultValue = "10") Integer pageSize) {
        EsAlterUtil.checkPageRange(pageNum, pageSize, null);

        EsIndexEnum selectIndex = EsIndexEnum.getEsIndexByIndex(index);
        if (EsIndexEnum.PATENT.getEsIndex().equals(selectIndex.getEsIndex())){
            EsPageResult resultMap = patentEsInfoService.querySimilarPatent(id, pageNum, pageSize);

            if (CollectionUtils.isEmpty(resultMap.getList())) {
                return ResultConvert.success(resultMap);
            }

            for (Map<String, Object> record : resultMap.getList()) {
                if (StringUtils.isNotEmpty((String)record.get("title"))){
                    record.put("name", record.get("title") + "(" + record.get("public_code") + ")");
                }
            }
            return ResultConvert.success(resultMap);
        }
        final Map<String, Object> source = elasticsearchHelper.getDataById(selectIndex.getEsIndex(), id, new String[]{"chain", "chain_node", "simple_family"}, null);
        if (source == null || source.isEmpty()) {
            return ResultConvert.success(new EsPageResult());
        }
        if ((source.get("chain") == null || CollectionUtils.isEmpty((List<Map<String, Object>>) source.get("chain")))
                && (source.get("simple_family") == null || CollectionUtils.isEmpty((List<Object>) source.get("simple_family")))) {
            return ResultConvert.success(new EsPageResult());
        }
        // 仅处理项目和专利类型
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        if (CollectionUtils.isNotEmpty((List<Map<String, Object>>) source.get("chain"))) {
            BoolQueryBuilder chainQuery = QueryBuilders.boolQuery();
            String chainId = (String) ((List<Map<String, Object>>) source.get("chain")).get(0).get("id");
            // 按节点推送相似项目
            chainQuery.filter(QueryBuilders.termQuery("chain.id", chainId));
            if (CollectionUtils.isNotEmpty((List<Map<String, Object>>) source.get("chain_node"))) {
                for (Map<String, Object> chainNode : (List<Map<String, Object>>) source.get("chain_node")) {
                    chainQuery.should(QueryBuilders.matchQuery("chain_node.id", chainNode.get("id")));
                }
            }
            chainQuery.mustNot(QueryBuilders.termQuery("id", id));
            queryBuilder.should(chainQuery);
        }
        if (CollectionUtils.isNotEmpty((List<Object>) source.get("simple_family"))) {
            BoolQueryBuilder similarQuery = QueryBuilders.boolQuery();
            List<Object> similarCNs = (List<Object>) source.get("simple_family");
            similarQuery.filter(QueryBuilders.termsQuery("apply_code", similarCNs));
            queryBuilder.should(similarQuery);
        }
        queryBuilder.minimumShouldMatch(1);
        EsSimpleQuery esSimpleQuery = new EsSimpleQuery();
        esSimpleQuery.setPageNum(pageNum);
        esSimpleQuery.setPageSize(pageSize);
        EsPageResult pageResult = EsAlterUtil.esPageSearch(elasticsearchHelper, esSimpleQuery, selectIndex, queryBuilder, null);
        return ResultConvert.success(pageResult);
    }

    private Map<String, Object> buildSourceMap(String field, String id, EsIndexEnum esIndexEnum) {
        SearchSourceBuilder searchBuilder = new SearchSourceBuilder();
        QueryBuilder productQuery = QueryBuilders.termQuery(field, id);
        searchBuilder.fetchSource(esIndexEnum.getEsSearchFields(), null);
        searchBuilder.query(productQuery);
        searchBuilder.size(10000);
        EsAlterUtil.addSort(searchBuilder, esIndexEnum.getSort(), null);
        SearchResponse searchResponse = elasticsearchHelper.pageByFields(searchBuilder, esIndexEnum.getEsIndex());
        Map<String, Object> resultMap = ElasticsearchBuilder.buildPageResult(searchResponse);
        return resultMap;
    }

    @ApiOperation("数据总量")
    @GetMapping("/count")
    //@AntiReptile
    @SaCheckLogin
    public Result count() {
//        Set<String> chainIds = chainScopeService.getCurrentUserPermittedChainIds();
//        List<String> esIndexList = Arrays.asList("nanping_innovation_company", "nanping_innovation_company_meta", "nanping_innovation_patent", "nanping_innovation_company_financing",
//                "nanping_innovation_expert", "nanping_innovation_global_patent", "nanping_innovation_policy", "nanping_innovation_news", "nanping_innovation_platform",
//                "nanping_innovation_bottleneck", "nanping_innovation_demand", "nanping_innovation_achievement", "nanping_innovation_project");
//        Long count = 0L;
//        for (String esIndex : esIndexList) {
//            BoolQueryBuilder queryBuilders = QueryBuilders.boolQuery();
//            if (CollectionUtils.isNotEmpty(chainIds) && chainIds.size() < 8) {
//                queryBuilders.filter(QueryBuilders.termsQuery("chain.id", chainIds));
//            }
//            Long num = elasticsearchHelper.countRequest(esIndex, queryBuilders);
//            if ("nanping_innovation_patent".equals(esIndex)){
//                //num = num * 3;
//                num = 101131728L;
//            }
//            count += num;
//            log.error("库：{}数据总量为：{}", esIndex, num);
//        }
//        // 暂时写死海关数据总量
//        count += 281532892L;
//        return ResultConvert.success(String.valueOf(501812037L));
        return ResultConvert.success(String.valueOf(knowledgeCountService.getKnowledgeCount()));
    }


}
