package com.quantchi.nanping.innovation.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.config.aop.Log;
import com.quantchi.nanping.innovation.config.aop.Metrics;
import com.quantchi.nanping.innovation.config.aop.PlatformAuthCheck;
import com.quantchi.nanping.innovation.demand.model.enums.DemandTypeEnum;
import com.quantchi.nanping.innovation.insight.model.IndexFusion;
import com.quantchi.nanping.innovation.insight.model.bo.ExpertPageBo;
import com.quantchi.nanping.innovation.insight.service.IIndexFusionService;
import com.quantchi.nanping.innovation.model.IndustryChainNode;
import com.quantchi.nanping.innovation.model.IndustryChainNodeTarget;
import com.quantchi.nanping.innovation.model.IndustryChainNodeType;
import com.quantchi.nanping.innovation.model.IndustryChainNodeWeak;
import com.quantchi.nanping.innovation.model.bo.*;
import com.quantchi.nanping.innovation.model.constant.CommonConstant;
import com.quantchi.nanping.innovation.model.constant.Constant;
import com.quantchi.nanping.innovation.model.enums.EsIndexEnum;
import com.quantchi.nanping.innovation.model.enums.index.FusionIndexEnum;
import com.quantchi.nanping.innovation.model.vo.PieVO;
import com.quantchi.nanping.innovation.service.HomeService;
import com.quantchi.nanping.innovation.service.IIndustryChainNodeTargetService;
import com.quantchi.nanping.innovation.service.IndexService;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.service.library.*;
import com.quantchi.nanping.innovation.utils.BigDecimalUtil;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import com.quantchi.tianying.model.EsPageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023/1/9 10:15
 */
@RestController
@Api(tags = "驾驶舱模块")
@RequestMapping("/cockpit")
@Validated
@PlatformAuthCheck(type = {"0"})
@Metrics
public class CockpitController {

    @Autowired
    private HomeService homeService;

    @Autowired
    private IndexService indexService;

    @Autowired
    private PatentService patentService;

    @Autowired
    private FinanceService financeService;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private IndustryChainService industryChainService;

    @Autowired
    private TalentService talentService;

    @Autowired
    private DemandService demandService;

    @Autowired
    private RiskService riskService;

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @Autowired
    private IIndexFusionService indexFusionService;

    @Autowired
    private IIndustryChainNodeTargetService nodeTargetService;

    @ApiOperation(value = "区域详情")
    @GetMapping("/regionDetail")
    //@Log(title = "驾驶舱-区域详情")
    public Result<JSONObject> regionDetail(String chainId, @RequestParam(required = true) String regionId) {
        IndexQuery indexQuery = new IndexQuery();
        indexQuery.setChainId(chainId);
        indexQuery.setRegionId(regionId);
        indexQuery.setChainNodeId(Constant.INDEX_EMPTY_CONDITION);
        JSONObject res = new JSONObject();
        res.put("fusion", indexService.getFusionIndex(indexQuery));
        res.put("index", homeService.list4RegionDetail(chainId, regionId));
        return ResultConvert.success(res);
    }

//    @ApiOperation(value = "区域列表")
//    @GetMapping("/regionList")
//    //@Log(title = "驾驶舱-区域列表")
//    public Result<List<CockpitGeoBO>> regionDetail(String chainId) {
//        if (StringUtils.isEmpty(chainId)) {
//            chainId = Constant.INDEX_EMPTY_CONDITION;
//        }
//        return ResultConvert.success(homeService.listRegion(chainId));
//    }

    @ApiOperation(value = "创新链-关键技术类型统计")
    @GetMapping("/innovation/tech")
    @Log(title = "驾驶舱")
    public Result<List<CommonIndexBO>> innovationTechDetail(String chainId) {
        Map<String, Long> countMap = riskService.getRiskTypeMap(chainId);
        return ResultConvert.success(CommonIndexBO.buildList(countMap));
    }

    @ApiOperation(value = "创新链-薄弱节点列表")
    @GetMapping("/innovation/weak")
    //@Log(title = "驾驶舱-创新链-薄弱节点列表")
    public Result<Page<IndustryChainNodeWeak>> innovationWeakDetail(String chainId, int pageNum, int pageSize) {
        return ResultConvert.success(industryChainService.page4WeakNode(chainId, pageNum, pageSize));
    }

    @ApiOperation(value = "产业链-链上企业分布")
    @GetMapping("/industry/company")
    //@Log(title = "驾驶舱-产业链-链上企业分布")
    public Result<Map<String, Object>> industryDetail(String chainId, String regionId) {
        Map<String, Object> resultMap = new LinkedHashMap<>();
        // 链上企业分布
        // 链上企业分布-链上企业数量
        resultMap.put("companyNum", companyService.countNumInChain(chainId, null, CommonConstant.DIVISION_NANPING.getId(), regionId, false));
        // 链上企业分布-布局产业链节点（挂载企业的节点）
        resultMap.put("nodeNum", companyService.countNodeNumRelatedCompany(chainId, CommonConstant.DIVISION_NANPING.getId(), regionId, false));
        // 链上企业分布-企业类型统计
        resultMap.put("typeCount", companyService.getCompanyTypeAnalyze(chainId, null, CommonConstant.DIVISION_NANPING.getId(), regionId, false));
        return ResultConvert.success(resultMap);
    }

    @ApiOperation(value = "产业链-链节点分布")
    @GetMapping("/industry/node")
    //@Log(title = "驾驶舱-产业链-链节点分布")
    public Result<Map<String, Object>> industryDetail(String chainId, Integer nodeTypeId) {
        Map<String, Object> resultMap = new LinkedHashMap<>();
        // 链节点分布
        // 链节点分布-节点名称列表
        Page<Map<String, Object>> nodeTypePage = industryChainService.page4NodeType(0, 200, chainId, null, nodeTypeId, null);
        nodeTypePage.getRecords().removeIf(node -> node.get("node_type") == null);
        resultMap.put("nodes", nodeTypePage.getRecords());
        return ResultConvert.success(resultMap);
    }

    @ApiOperation(value = "产业链-链节点分布-圆环图统计")
    @GetMapping("/industry/node/distribution")
    public Result<PieVO> getNodeDistribution(@NotBlank String chainId) {
        PieVO result = new PieVO();
        List<CommonIndexBO> indexBOList = industryChainService.countNodeTypeMapByChainId(chainId, null, false);
        result.setIndexList(indexBOList);
        Long totalNum = 0L;
        for (CommonIndexBO bo : indexBOList) {
            totalNum += Long.parseLong(String.valueOf(bo.getData()));
        }
        result.setTotal(totalNum);
        return ResultConvert.success(result);
    }

    @ApiOperation(value = "产业链-靶向企业列表")
    @PostMapping("/industry/target")
    //@Log(title = "驾驶舱-产业链-靶向企业列表")
    public Result<EsPageResult> listTargetCompany(@RequestBody CompanyPageBo pageBo) {
        List<String> nodeIds = pageBo.getNodeIdList();
        if (CollectionUtils.isEmpty(nodeIds)) {
            List<IndustryChainNodeType> nodeTypeList = industryChainService.listByNodeType(pageBo.getChainId(), pageBo.getNodeTypeId());
            nodeIds = nodeTypeList.stream().map(IndustryChainNodeType::getNodeId).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(nodeIds)) {
            return ResultConvert.success(new EsPageResult());
        }
        pageBo.setTarget(true);
        pageBo.setNodeIdList(nodeIds);
        // 判断是否指定了靶向企业
        EsPageResult companyPageSpecified = nodeTargetService.pageByNodeIds(pageBo, nodeIds);
        pageBo.setCompanyIds(null);
        EsPageResult companyPageFromES = companyService.getCompanyList(pageBo);

        EsPageResult companyPage = new EsPageResult();
        if (companyPageSpecified != null && companyPageSpecified.getTotal() > 0) {
            if (companyPageSpecified.getList().size() < pageBo.getPageSize() && companyPageFromES.getTotal() != null) {
                List<String> companyIds = companyPageSpecified.getList().stream().map(c ->
                        Optional.ofNullable(c.get("id")).map(Object::toString).orElse("")).collect(Collectors.toList());
                List<Map<String, Object>> coompanyFromEs = companyPageFromES.getList().stream()
                        .filter(c ->
                                !companyIds.contains(Optional.ofNullable(c.get("id")).map(Object::toString).orElse("")))
                        .collect(Collectors.toList());
                companyPage.setTotal(companyPageSpecified.getTotal() + coompanyFromEs.size());
                companyPageSpecified.getList().addAll(coompanyFromEs);
                companyPage.setList(companyPageSpecified.getList().size() > pageBo.getPageSize() ?
                        companyPageSpecified.getList().subList(0, pageBo.getPageSize()) :
                        companyPageSpecified.getList());
            } else {
                companyPage.setTotal(companyPageSpecified.getTotal());
                companyPage.setList(companyPageSpecified.getList());
            }
        } else {
            companyPage.setTotal(companyPageFromES.getTotal());
            companyPage.setList(companyPageFromES.getList());
        }


        // 产业链节点仅保存当前选中产业链
        EsAlterUtil.filterChainByChainId(companyPage.getList(), pageBo.getChainId());
        return ResultConvert.success(companyPage);
    }

    @ApiOperation(value = "资金链-投资领域分析")
    @GetMapping("/finance/domain")
    //@Log(title = "驾驶舱-资金链-投资领域分析")
    public Result<PieVO> financeDomainDetail(String chainId, boolean local) {
        List<IndustryChainNode> targetNodes = industryChainService.getNodesByChainIdAndLevels(chainId, 2, 2);
        Map<String, String> targetNodeNameMap = targetNodes.stream().collect(Collectors.toMap(IndustryChainNode::getId, IndustryChainNode::getName));
        PieVO vo = new PieVO();
        BigDecimal financeAmount = financeService.sumByChainAndRegion(chainId, null, local ? CommonConstant.DIVISION_NANPING.getId() : null, null, null);
        BoolQueryBuilder boolQueryBuilder = financeService.buildBaseQuery(chainId, null, null,
                local ? CommonConstant.DIVISION_NANPING.getId() : null, null);
        if (CollectionUtils.isNotEmpty(targetNodeNameMap.keySet())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("chain_node.id", targetNodeNameMap.keySet()));
        }
        Map<String, BigDecimal> nodeCountMap = EsAlterUtil.getSumAggregation(elasticsearchHelper, EsIndexEnum.FINANCING, boolQueryBuilder, "chain_node.name", "financing_amount_cal");
        List<CommonIndexBO> boList = new ArrayList<>();
        BigDecimal unit = new BigDecimal(*********);
        for (IndustryChainNode node : targetNodes) {
            boList.add(new CommonIndexBO(node.getName(), nodeCountMap.containsKey(node.getName()) ?
                    nodeCountMap.get(node.getName()).divide(unit, 2, RoundingMode.HALF_UP) : 0));
        }
        vo.setIndexList(boList);
        vo.setTotal(financeAmount.divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP));
        return ResultConvert.success(vo);
    }

    @ApiOperation(value = "资金链-投资机构列表")
    @GetMapping("/finance/org")
    //@Log(title = "驾驶舱-资金链-投资机构列表")
    @Deprecated
    public Result<List<FinanceCountBo>> financeDetail(@NotBlank String chainId) {
        return ResultConvert.success(financeService.getInvestmentFund(chainId).subList(0, 3));
    }

    @PostMapping("/finance/event")
    @ApiOperation("资金链-投融资事件")
    //@Log(title = "驾驶舱-资金链-投融资事件")
    @Deprecated
    public Result getEventList(@Validated @RequestBody FinanceEventBo financeEventBo) {
        return ResultConvert.success(financeService.getEventList(financeEventBo));
    }

    @GetMapping("/finance/compare")
    @ApiOperation("资金链-风险投资规模对比")
    //@Log(title = "驾驶舱-资金链-风险投资规模对比")
    @Deprecated
    public Result compareFinanceSale(@NotBlank String chainId) {
        BoolQueryBuilder boolQueryBuilder = financeService.buildBaseQuery(chainId, null, null, null, null);
        Map<String, BigDecimal> amountCountMap = EsAlterUtil.getSumAggregation(elasticsearchHelper, EsIndexEnum.FINANCING, boolQueryBuilder,
                "financing_company.province_code", "financing_amount_cal");
        BigDecimal totalAmount = BigDecimal.ZERO, provinceAmount = BigDecimal.ZERO;
        for (Map.Entry<String, BigDecimal> count : amountCountMap.entrySet()) {
            totalAmount = totalAmount.add(count.getValue());
            if (CommonConstant.DIVISION_FUJIAN.getId().equals(count.getKey())) {
                provinceAmount = count.getValue();
            }
        }
        BigDecimal cityAmount = financeService.sumByChainAndRegion(chainId, null, CommonConstant.DIVISION_NANPING.getId(), null, null);
        List<CommonIndexBO> boList = new ArrayList<>();
        boList.add(new CommonIndexBO("南平", BigDecimalUtil.divide(cityAmount, new BigDecimal(10000), 2, RoundingMode.HALF_UP), null));
        boList.add(new CommonIndexBO("福建", BigDecimalUtil.divide(provinceAmount, new BigDecimal(*********), 2, RoundingMode.HALF_UP), null));
        boList.add(new CommonIndexBO("全国", BigDecimalUtil.divide(totalAmount, new BigDecimal(*********), 2, RoundingMode.HALF_UP), null));
        return ResultConvert.success(boList);
    }

    @PostMapping("/finance/need")
    @ApiOperation("资金链-企业融资需求")
    //@Log(title = "驾驶舱-资金链-企业融资需求")
    public Result getFinancingNeeds(@RequestBody ChainNodeBo bo) {
        return ResultConvert.success(financeService.getFinancingNeeds(bo.getChainId()));
    }

    @ApiOperation(value = "人才链-本地人才、需求供给分析")
    @GetMapping("/expert/expert_demand")
    //@Log(title = "驾驶舱-人才链-本地人才、需求供给分析")
    //@Deprecated
    public Result<Map<String, Object>> expertCompareDetail(String chainId) {
        List<IndustryChainNode> targetNodes = industryChainService.getNodesByChainIdAndLevels(chainId, 2, 2);
        Map<String, String> targetNodeNameMap = targetNodes.stream().collect(Collectors.toMap(IndustryChainNode::getId, IndustryChainNode::getName));
        BoolQueryBuilder personQuery = EsAlterUtil.buildAuthQuery(chainId, null, CommonConstant.DIVISION_NANPING.getId(), null, false);
        if (CollectionUtils.isNotEmpty(targetNodeNameMap.keySet())) {
            personQuery.filter(QueryBuilders.termsQuery("chain_node.id", targetNodeNameMap.keySet()));
        }
        Map<String, Long> personMap = EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.EXPERT, personQuery, "chain_node.name");
        BoolQueryBuilder demandQuery = EsAlterUtil.buildAuthQuery(chainId, null, null, null, false);
        if (CollectionUtils.isNotEmpty(targetNodeNameMap.keySet())) {
            demandQuery.filter(QueryBuilders.termsQuery("chain_node.id", targetNodeNameMap.keySet()));
        }
        Map<String, Long> demandMap = EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.DEMAND, demandQuery, "chain_node.name");
        Map<String, Object> resultMap = new LinkedHashMap<>();
        for (IndustryChainNode node : targetNodes) {
            Map<String, Object> nodeCount = new LinkedHashMap<>();
            nodeCount.put("expertNum", personMap.get(node.getName()));
            nodeCount.put("demandNum", demandMap.get(node.getName()));
            nodeCount.put("nodeId", node.getId());
            resultMap.put(node.getName(), nodeCount);
        }
        return ResultConvert.success(resultMap);
    }

    @ApiOperation(value = "人才链-人才需求")
    @GetMapping("/expert/demand")
    //@Log(title = "驾驶舱-人才链-人才需求")
    public Result<List<Map<String, Object>>> listDemand(String chainId) {
        return ResultConvert.success(demandService.listDemandByChainId(chainId, null));
    }

    @ApiOperation(value = "人才链-可引进人才分析")
    @GetMapping("/expert/import")
    //@Log(title = "驾驶舱-人才链-可引进人才分析")
    public Result<PieVO> expertImportDetail(String chainId, boolean inProvince) {
        BoolQueryBuilder personQuery = EsAlterUtil.buildAuthQuery(chainId, null, null, null, false);
        if (inProvince) {
            // 20231017 改为查询南平市
            personQuery.filter(QueryBuilders.termQuery("city.id", CommonConstant.DIVISION_NANPING.getId()));
        } else {
            // 20231101
            personQuery.mustNot(QueryBuilders.termQuery("city.id", CommonConstant.DIVISION_NANPING.getId()));
        }
        Long totalNum = elasticsearchHelper.countRequest(EsIndexEnum.EXPERT.getEsIndex(), personQuery);
        // 查询节点分布
        List<IndustryChainNode> targetNodes = industryChainService.getNodesByChainIdAndLevels(chainId, 2, 2);
        Map<String, String> targetNodeNameMap = targetNodes.stream().collect(Collectors.toMap(IndustryChainNode::getId, IndustryChainNode::getName));
        if (CollectionUtils.isNotEmpty(targetNodeNameMap.keySet())) {
            personQuery.filter(QueryBuilders.termsQuery("chain_node.id", targetNodeNameMap.keySet()));
        }
        Map<String, Long> personMap = EsAlterUtil.getAggregation(elasticsearchHelper, EsIndexEnum.EXPERT, personQuery, "chain_node.name");
        PieVO vo = new PieVO();
        List<CommonIndexBO> boList = new ArrayList<>();
        for (Map.Entry<String, Long> nodeCount : personMap.entrySet()) {
            if (targetNodeNameMap.values().contains(nodeCount.getKey())) {
                boList.add(new CommonIndexBO(nodeCount.getKey(), nodeCount.getValue(), null));
            }
        }
        Collections.sort(boList, (b1, b2) -> ((Long) b2.getData()).compareTo((Long) b1.getData()));
        vo.setIndexList(boList);
        vo.setTotal(totalNum);
        return ResultConvert.success(vo);
    }

    @ApiOperation(value = "人才链-外部人才推荐")
    @PostMapping("/expert/page")
    //@Log(title = "驾驶舱-人才链-外部人才推荐")
    public Result<EsPageResult> page4RecommendedExpert(@Validated @RequestBody ExpertPageBo pageBo) {
        return ResultConvert.success(talentService.page(pageBo));
    }

    @ApiOperation(value = "科特派系统接入")
    @GetMapping("/externalSystem")
    //@Log(title = "驾驶舱-科特派系统接入")
    public Result<Map<String, Object>> externalSystemDetail(@NotBlank String chainId) {
        Map<String, Object> resultMap = new LinkedHashMap<>();
        Pair<Long, BigDecimal> matchResult = demandService.countMatchedDemand(chainId);
        // 历史匹配需求数
        resultMap.put("demandNum", matchResult.getLeft());
        // 需求匹配率
        resultMap.put("matchRatio", matchResult.getRight());
        // 详细需求
        resultMap.put("demands", demandService.listSample(chainId).getList());
        return ResultConvert.success(resultMap);
    }

    @ApiOperation(value = "指标")
    @GetMapping("/index")
    //@Log(title = "驾驶舱-指标")
    public Result<Map<String, Object>> indexDetail(String chainId, String regionId) {
        Map<String, Object> resultMap = new LinkedHashMap<>();
        // 绿色创新指数 环比
        Pair<BigDecimal, BigDecimal> fusionPair = indexService.getFusionIndexByChainIdAndRegionId(chainId,
                StringUtils.isEmpty(regionId) ? CommonConstant.DIVISION_NANPING.getId() : regionId);
        resultMap.put("techPoint", fusionPair.getLeft());
        resultMap.put("increase", fusionPair.getRight());
        // 专利数量
        resultMap.put("patentNum", patentService.countPatentNumByChainIdAndRegionId(chainId, null, CommonConstant.DIVISION_NANPING.getId(), regionId));
//        // RD占比
//        resultMap.put("rd", rdRatioService.getLatestByCityId(CommonConstant.DIVISION_NANPING.getId()));
        // 链上企业
        resultMap.put("companyNum", companyService.countNumInChain(chainId, null, CommonConstant.DIVISION_NANPING.getId(), regionId, false));
//        // 招商项目
//        resultMap.put("projectNum", projectService.countInvestmentAttract(chainId, null, CommonConstant.DIVISION_NANPING.getId(), regionId));
        // 投融资规模
        BigDecimal financeAmount = financeService.getStatisticByChain(chainId, CommonConstant.DIVISION_NANPING.getId(), regionId).get("financeAmount");
        if (financeAmount == null || financeAmount.compareTo(BigDecimal.ZERO) <= 0) {
            financeAmount = null;
        }
        resultMap.put("financeAmount", financeAmount);
        // 投融资笔数
        resultMap.put("financeNum", financeService.countFinanceNumByChain(chainId, CommonConstant.DIVISION_NANPING.getId(), regionId, null));
//        // 项目补助金额
//        resultMap.put("subsidy", projectService.countSubsidy(chainId, null, CommonConstant.DIVISION_NANPING.getId(), regionId));
        // 本地人才
        resultMap.put("localNum", talentService.countExpertNum(chainId, null, CommonConstant.DIVISION_NANPING.getId(), regionId, false));
//        // 科技特派员
//        resultMap.put("techCommissionNum", talentService.countExpertNum(chainId, null, CommonConstant.DIVISION_NANPING.getId(), regionId, true));
        return ResultConvert.success(resultMap);
    }

    @ApiOperation(value = "四链融合模型")
    @GetMapping("/fusion")
    //@Log(title = "驾驶舱-四链融合模型")
    public Result<Map<String, Object>> fusionDetail(String chainId) {
        return ResultConvert.success(indexFusionService.fusionDetail(chainId));
    }

    @ApiOperation(value = "四链融合分析列表")
    @GetMapping("/fusion/list")
    public Result<List<IndexFusion>> fusionList(@NotBlank String chainId) {
        List<IndexFusion> fusionList = indexFusionService.list(Arrays.asList(FusionIndexEnum.FUND_CHAIN.getIndexId(),
                        FusionIndexEnum.INDUSTRY_CHAIN.getIndexId(), FusionIndexEnum.INNOVATION_CHAIN.getIndexId(),
                        FusionIndexEnum.PERSON_CHAIN.getIndexId()), chainId, null,
                CommonConstant.DIVISION_NANPING.getId(), true, true);
        for (IndexFusion ifu : fusionList) {
            ifu.setIndexName(FusionIndexEnum.getNameById(ifu.getIndexId()));
        }
        Map<String, IndexFusion> fusionMap = fusionList.stream().collect(Collectors.toMap(IndexFusion::getIndexId, Function.identity()));
        List<IndexFusion> sortedFusionList = new ArrayList<>();
        sortedFusionList.add(fusionMap.get(FusionIndexEnum.INDUSTRY_CHAIN.getIndexId()));
        sortedFusionList.add(fusionMap.get(FusionIndexEnum.INNOVATION_CHAIN.getIndexId()));
        sortedFusionList.add(fusionMap.get(FusionIndexEnum.PERSON_CHAIN.getIndexId()));
        sortedFusionList.add(fusionMap.get(FusionIndexEnum.FUND_CHAIN.getIndexId()));
        return ResultConvert.success(sortedFusionList);
    }

    @GetMapping("/get/fund/acquisition")
    @ApiOperation("资金获取情况")
    //@Log(title = "资金链-资金获取情况")
    public Result listFundAcquisition(String chainId) {
        return ResultConvert.success(financeService.listFundAcquisitionObject(chainId));
    }

}
