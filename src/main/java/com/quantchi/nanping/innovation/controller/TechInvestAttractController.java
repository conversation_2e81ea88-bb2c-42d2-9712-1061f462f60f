package com.quantchi.nanping.innovation.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quantchi.nanping.innovation.common.Result;
import com.quantchi.nanping.innovation.common.ResultConvert;
import com.quantchi.nanping.innovation.config.aop.Log;
import com.quantchi.nanping.innovation.config.aop.Metrics;
import com.quantchi.nanping.innovation.config.aop.PlatformAuthCheck;
import com.quantchi.nanping.innovation.insight.service.IIndexFusionService;
import com.quantchi.nanping.innovation.model.IndustryChainNodeWeak;
import com.quantchi.nanping.innovation.model.bo.CommonIndexBO;
import com.quantchi.nanping.innovation.model.bo.CompanyPageBo;
import com.quantchi.nanping.innovation.model.bo.FinanceCountBo;
import com.quantchi.nanping.innovation.model.enums.index.FusionIndexEnum;
import com.quantchi.nanping.innovation.overcome.service.OvercomeService;
import com.quantchi.nanping.innovation.service.IndustryChainService;
import com.quantchi.nanping.innovation.service.library.*;
import com.quantchi.nanping.innovation.utils.EsAlterUtil;
import com.quantchi.nanping.innovation.utils.RequestContext;
import com.quantchi.tianying.model.EsPageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/8 11:12
 */
@Slf4j
@RestController
@RequestMapping("/tech_attract")
@Api(tags = "科技招商")
@Validated
@PlatformAuthCheck(type = {"0"})
@Metrics
public class TechInvestAttractController {

    @Autowired
    private IIndexFusionService indexFusionService;

    @Autowired
    private IndustryChainService industryChainService;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private TalentService talentService;

    @Autowired
    private AchievementService achievementService;

    @Autowired
    private PatentService patentService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private FinanceService financeService;

    @Autowired
    private PlatformService platformService;

    @Autowired
    private RiskService riskService;

    @Autowired
    private OvercomeService overcomeService;

    @ApiOperation(value = "创新链诊断")
    @GetMapping("/chain_analyze")
    @Log(title = "科技招商")
    public Result<Map<String, Object>> analyzeInnovationChain(@NotBlank @RequestParam String chainId) {
        Map<String, Object> resultMap = new LinkedHashMap<>();
        String cityId = RequestContext.getCityId(), areaId = RequestContext.getAreaId();
        // 整体情况
        List<CommonIndexBO> overviewBoList = new ArrayList<>();
        // 1.创新得分
        overviewBoList.add(new CommonIndexBO("创新链指数", indexFusionService.getOne(FusionIndexEnum.INNOVATION_CHAIN.getIndexId(), chainId,
                null, RequestContext.getRegionId(), true).getData(), "分"));
        // 2.链上节点
        overviewBoList.add(new CommonIndexBO("链上节点", industryChainService.countNodes(chainId), "个"));
        // 3.链上企业
        overviewBoList.add(new CommonIndexBO("链上企业", companyService.countNumInChain(chainId, null, cityId, areaId, false), "家"));
        // 4.链上人才
        overviewBoList.add(new CommonIndexBO("链上人才", talentService.countExpertNum(chainId, null, cityId, areaId, false), "人"));
        // 5.链上成果
        overviewBoList.add(new CommonIndexBO("链上成果", achievementService.countByChain(chainId, null), "项"));
        // 6.链上专利
        overviewBoList.add(new CommonIndexBO("链上专利", patentService.countPatentNumByChainIdAndRegionId(chainId, null, cityId, areaId), "项"));
        resultMap.put("overview", overviewBoList);
        // 薄弱节点分析
        resultMap.put("weaknesses", industryChainService.listWeakNodesAnalysisByChainId(chainId));
        return ResultConvert.success(resultMap);
    }

    @ApiOperation(value = "招商项目进度分析")
    @GetMapping("/project_analyze")
    //@Log(title = "科技招商-招商项目进度分析")
    public Result<List<CommonIndexBO>> analyzeProject(@NotBlank @RequestParam String chainId) {
        return ResultConvert.success(projectService.countInvestmentAttractGroupByStatus(chainId));
    }

    @ApiOperation(value = "招商项目列表")
    @GetMapping("/project/page")
    //@Log(title = "科技招商-招商项目列表")
    public Result<EsPageResult> page4Project(@RequestParam(defaultValue = "1") Integer pageNum, @RequestParam(defaultValue = "5") Integer pageSize, @NotBlank @RequestParam String chainId) {
        return ResultConvert.success(projectService.page4InvestmentAttract(pageNum, pageSize, chainId));
    }

    @ApiOperation(value = "靶向企业来源分布, 同时使用在四链融合总览")
    @GetMapping("/company_analyze")
    //@Log(title = "科技招商-靶向企业来源分布")
    public Result<List<CommonIndexBO>> analyzeCompany(@NotBlank @RequestParam String chainId, boolean isWeak, String techId) {
        BoolQueryBuilder boolQueryBuilder = companyService.buildTargetCompanyQuery(chainId, null);
        if (isWeak) {
            boolQueryBuilder = QueryBuilders.boolQuery();
            List<IndustryChainNodeWeak> weakNodes = industryChainService.listWeakNodesByChainId(chainId);
            if (CollectionUtils.isNotEmpty(weakNodes)) {
                Set<String> companyIds = weakNodes.stream().filter(w -> StringUtils.isNotEmpty(w.getCompanyId())
                                && (StringUtils.isEmpty(techId) || w.getDataId().equals(techId)))
                        .map(IndustryChainNodeWeak::getCompanyId).collect(Collectors.toSet());
                boolQueryBuilder.filter(QueryBuilders.idsQuery().addIds(companyIds.toArray(new String[0])));
            } else {
                return ResultConvert.success(new ArrayList<>(0));
            }
        }
        return ResultConvert.success(companyService.getCompanyCountByProvince(boolQueryBuilder));
    }

    @ApiOperation(value = "靶向企业列表, 同时使用在四链融合总览")
    @GetMapping("/company/page")
    //@Log(title = "科技招商-靶向企业列表")
    public Result<EsPageResult> page4Company(@RequestParam(defaultValue = "1") Integer pageNum, @RequestParam(defaultValue = "5") Integer pageSize,
                                             @NotBlank @RequestParam String chainId, boolean isWeak, boolean fromCockpit, String techId) {
        CompanyPageBo bo = new CompanyPageBo();
        if (!isWeak) {
            bo.setTarget(true);
            bo.setPageNum(pageNum);
            bo.setPageSize(pageSize);
        }
        bo.setChainId(chainId);
        Map<String, List<String>> companyWeakMap = new HashMap<>();
        Page<Map<String, Object>> weakNodePage = new Page<>();
        List<Map<String, Object>> weakNodes = new ArrayList<>();
        if (isWeak) {
            // 竹产业链单独展示本地优势企业
            if ("1003".equals(chainId) && fromCockpit) {
                List<String> companyIds = riskService.getCityAdvantageCompanyIdsByChainId(chainId);
                if (CollectionUtils.isEmpty(companyIds)) {
                    return ResultConvert.success(new EsPageResult());
                }
                CompanyPageBo pageBo = new CompanyPageBo();
                pageBo.setCompanyIds(companyIds);
                pageBo.setPageNum(pageNum);
                pageBo.setPageSize(pageSize);
                EsPageResult pageResult = companyService.getCompanyList(pageBo);
                return ResultConvert.success(pageResult);
            }
            weakNodePage = industryChainService.pageWeakNodeCompanyByChainId(new Page<>(pageNum, pageSize), chainId, techId);
            weakNodes = weakNodePage.getRecords();
            if (CollectionUtils.isNotEmpty(weakNodes)) {
                Set<String> companyIds = new HashSet<>();
                for (Map<String, Object> weak : weakNodes) {
                    companyIds.add((String) weak.get("company_id"));
                }
                bo.setCompanyIds(new ArrayList<>(companyIds));
                bo.setWeakTarget(true);
                List<IndustryChainNodeWeak> weakList = industryChainService.listWeakNodesByChainId(chainId);
                for (IndustryChainNodeWeak weak : weakList) {
                    companyWeakMap.computeIfAbsent(weak.getCompanyId(), k -> new ArrayList<>());
                    companyWeakMap.get(weak.getCompanyId()).add(weak.getName());
                }
            } else {
                return ResultConvert.success(new EsPageResult());
            }
        }
        EsPageResult pageResult = companyService.getCompanyList(bo);
        if (pageResult == null || CollectionUtils.isEmpty(pageResult.getList())) {
            return ResultConvert.success(pageResult);
        }
        List<Map<String, Object>> companyList = pageResult.getList();
        if (isWeak) {
            Map<String, Map<String, Object>> companyMap = new HashMap<>();
            for (Map<String, Object> company : companyList) {
                company.put("weakNodes", companyWeakMap.get(company.get("id")));
                companyMap.put((String) company.get("id"), company);
            }
            for (Map<String, Object> weak : weakNodes) {
                String companyId = (String) weak.get("company_id");
                if (companyMap.containsKey(companyId) && companyMap.get(companyId).size()>0){
                    weak.putAll(companyMap.get(companyId));
                }
            }
            companyList = weakNodes;
            pageResult.setTotal(weakNodePage.getTotal());
            pageResult.setPageSize(pageSize);
        }
        EsAlterUtil.filterChainByChainId(companyList, chainId);
        return ResultConvert.success(pageResult);
    }

    @ApiOperation(value = "产业投融资金额趋势")
    @GetMapping("/fund_tend")
    //@Log(title = "科技招商-产业投融资金额趋势")
    public Result<Map<String, Object>> getFundTend(@NotBlank @RequestParam String chainId) {
        return ResultConvert.success(financeService.getFinanceScaleTend(chainId, null, null));
    }

    @ApiOperation(value = "投资机构列表")
    @GetMapping("/invest_org")
    //@Log(title = "科技招商-投资机构列表")
    public Result<List<FinanceCountBo>> listInvestmentOrg(@NotBlank @RequestParam String chainId) {
        return ResultConvert.success(financeService.getInvestmentFund(chainId));
    }

    @ApiOperation(value = "国家级重点实验室分析")
    @GetMapping("/platform_analyze")
    //@Log(title = "科技招商-国家级重点实验室分析")
    public Result<List<CommonIndexBO>> analyzePlatform(@NotBlank @RequestParam String chainId) {
        return ResultConvert.success(platformService.countNationalGroupByProvince(chainId));
    }

    @ApiOperation(value = "国家级重点实验室列表")
    @GetMapping("/platform/page")
    //@Log(title = "科技招商-国家级重点实验室列表")
    public Result<EsPageResult> page4Platform(@RequestParam(defaultValue = "1") Integer pageNum, @RequestParam(defaultValue = "5") Integer pageSize, @NotBlank @RequestParam String chainId) {
        return ResultConvert.success(platformService.page4National(pageNum, pageSize, chainId));
    }

    @ApiOperation(value = "关键核心技术列表")
    @GetMapping("/weak/list")
    public Result<Map<String, String>> getWeakList(@NotBlank @RequestParam String chainId) {
        List<IndustryChainNodeWeak> weakList = industryChainService.listWeakNodesByChainId(chainId);
        Map<String, String> resultMap = new LinkedHashMap<>();
        for (IndustryChainNodeWeak weak : weakList) {
            resultMap.put(weak.getDataId(), weak.getName());
        }
        return ResultConvert.success(resultMap);
    }

    @GetMapping("/weak/type")
    @ApiOperation("关键核心技术分类统计")
    public Result<List<CommonIndexBO>> getRiskMap(String chainId) {
        Set<String> riskIdSet = null;
        if (!"1003".equals(chainId)){
            List<IndustryChainNodeWeak> weakList = industryChainService.listWeakNodesByChainId(chainId);
            riskIdSet = weakList.stream().map(IndustryChainNodeWeak::getDataId).collect(Collectors.toSet());
        }
        return ResultConvert.success(overcomeService.getRiskMap(chainId, riskIdSet));
    }

}
