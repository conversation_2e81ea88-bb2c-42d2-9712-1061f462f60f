package com.quantchi.nanping.innovation.service.impl;

import com.quantchi.nanping.innovation.insight.model.IndexFusion;
import com.quantchi.nanping.innovation.insight.service.IIndexFusionService;
import com.quantchi.nanping.innovation.model.bo.*;
import com.quantchi.nanping.innovation.service.library.TalentService;
import com.quantchi.nanping.innovation.model.enums.index.FusionIndexEnum;
import com.quantchi.nanping.innovation.service.*;
import com.quantchi.nanping.innovation.service.library.*;
import com.quantchi.nanping.innovation.utils.RequestContext;
import com.quantchi.nanping.innovation.utils.TaskUtil;
import com.quantchi.tianying.helper.ElasticsearchHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023/1/11 21:05
 */
@Service
@Slf4j
public class IndexServiceImpl implements IndexService {

    @Autowired
    private IIndexFusionService indexFusionService;

    @Autowired
    private ThreadPoolTaskExecutor chainExecutor;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private ElasticsearchHelper elasticsearchHelper;

    @Autowired
    private PlatformService platformService;

    @Autowired
    private RiskService riskService;

    @Autowired
    private AchievementService achievementService;

    @Autowired
    private FinanceService financeService;

    @Autowired
    private TalentService talentService;

    @Autowired
    private IndustryChainService industryChainService;

    @Autowired
    private PatentService patentService;

    @Autowired
    private ProjectService projectService;

    @Override
    public TwoLevelIndex getFusionIndex(IndexQuery indexQuery) {
        TwoLevelIndex res = new TwoLevelIndex();
        List<IndexFusion> indexDataList = indexFusionService.list(FusionIndexEnum.listIndexIds(), indexQuery.getChainId(), indexQuery.getChainNodeId(),
                indexQuery.getRegionId(), indexQuery.isNode(), true);
        Map<String, BigDecimal> indexDataMap = indexDataList.stream().collect(Collectors.toMap(IndexFusion::getIndexId, IndexFusion::getData));
        FusionIndexEnum resIndex = FusionIndexEnum.getParentIndexEnum();
        res.setName(resIndex.getAlias());
        res.setData(indexDataMap.get(resIndex.getIndexId()));
        for (FusionIndexEnum indexEnum : FusionIndexEnum.values()) {
            if (indexEnum.getParent()) {
                continue;
            }
            CommonIndexBO c1 = new CommonIndexBO(indexEnum.getIndexName(), indexDataMap.get(indexEnum.getIndexId()));
            res.getChildList().add(c1);
        }
        return res;
    }

    @Override
    public List<CommonIndexBO> getFusionPortalIndex(String chainId) {
        List<CommonIndexBO> resultList = new ArrayList<>();
        final String cityId = RequestContext.getCityId(), areaId = RequestContext.getAreaId();
        // 企业数
        final CompletableFuture<Void> companyFuture = CompletableFuture.runAsync(() -> {
            CompanyAnalyzeBo bo = companyService.getCompanyTypeAnalyze(chainId, null, cityId, areaId, false);
            resultList.add(new CommonIndexBO("总企业数", bo.getTotalNum(), "家"));
            resultList.add(new CommonIndexBO("科技型中小企业", bo.getMiddleAndSmallNum(), "家"));
            resultList.add(new CommonIndexBO("高新技术企业", bo.getHighTechNum(), "家"));
            resultList.add(new CommonIndexBO("科技小巨人企业", bo.getGiantNum(), "家"));
            resultList.add(new CommonIndexBO("其他企业", bo.getTotalNum() - bo.getMiddleAndSmallNum() - bo.getHighTechNum() - bo.getGiantNum(), "家"));
        }, chainExecutor);
        // 节点数
        final CompletableFuture<Void> nodeFuture = CompletableFuture.runAsync(() -> {
            Long totalNum = industryChainService.countNodes(chainId);
            resultList.add(new CommonIndexBO("总节点", totalNum, "个"));
            resultList.addAll(industryChainService.countNodeTypeMapByChainId(chainId, null, false));
        }, chainExecutor);
        // 平台数
        final CompletableFuture<Void> platformFuture = CompletableFuture.runAsync(() -> {
            Map<String, Long> platformMap = platformService.getPlatformTypeMap(chainId, cityId, areaId);
            resultList.add(new CommonIndexBO("平台载体", platformMap.values().stream().reduce(Long::sum).orElse(0L), "个"));
            resultList.add(new CommonIndexBO("企业技术中心", platformMap.containsKey("企业技术中心") ? platformMap.get("企业技术中心") : 0L, "个"));
        }, chainExecutor);
        // 专利数
        final CompletableFuture<Void> patentFuture = CompletableFuture.runAsync(() -> {
            Long totalNum = patentService.countPatentNumByChainIdAndRegionId(chainId, null, cityId, areaId);
            Long inventNum = patentService.countPatentTypeNumByChainIdAndRegion(chainId, cityId, areaId);
            resultList.add(new CommonIndexBO("专利数", totalNum, "件"));
            resultList.add(new CommonIndexBO("发明专利", inventNum, "件"));
            resultList.add(new CommonIndexBO("其他专利", totalNum - inventNum, "件"));
        }, chainExecutor);
        // 人才数
        final CompletableFuture<Void> personFuture = CompletableFuture.runAsync(() -> {
//            PersonStatsBo rankBo = new PersonStatsBo(chainId, industryChainService.getParentNodeIdByChainId(chainId), cityId, areaId, "E", true);
            Long totalNum = talentService.countExpertNum(chainId, null, cityId, areaId, false);
            Long masterNum = talentService.countMasterPlusNum(chainId, null, cityId, areaId, false);
            resultList.add(new CommonIndexBO("本地人才", totalNum, "人"));
            resultList.add(new CommonIndexBO("硕士以上人才", masterNum, "人"));
            resultList.add(new CommonIndexBO("其他人才", totalNum - masterNum, "人"));
        }, chainExecutor);
        // 风险清单
        final CompletableFuture<Void> riskFuture = CompletableFuture.runAsync(() -> {
            Map<String, Long> riskMap = riskService.getRiskTypeMap(chainId);
            List<String> typeList = Arrays.asList("省内有基础实现攻关突破", "尚无国产替代方案", "省外有基础实现攻关突破", "市内有基础实现攻关突破", "已实现国产化替代", "有可能抢占技术制高点");
            resultList.add(new CommonIndexBO("关键技术", riskMap.values().stream().reduce(Long::sum).orElse(0L), "项"));
            for (String type: typeList){
                if (!riskMap.containsKey(type)){
                    continue;
                }
                resultList.add(new CommonIndexBO(type, riskMap.containsKey(type)? riskMap.get(type):0L, "个"));
            }
        }, chainExecutor);
        // 项目
        final CompletableFuture<Void> projectFuture = CompletableFuture.runAsync(() -> {
            Long totalNum = projectService.count(chainId, null, cityId, areaId);
            Map<String, Long> projectTypeMap = projectService.getProjectCountMap(chainId, null, cityId, areaId);
            resultList.add(new CommonIndexBO("本地项目", totalNum, "个"));
            Long otherNum = 0L;
            int topLimit = 1;
            for (Map.Entry<String, Long> count: projectTypeMap.entrySet()){
                if (topLimit <= 3){
                    resultList.add(new CommonIndexBO(count.getKey(), count.getValue(), "个"));
                    topLimit++;
                }else{
                    otherNum+=count.getValue();
                }
            }
            if (otherNum > 0L){
                resultList.add(new CommonIndexBO("其他项目", otherNum, "个"));
            }
        }, chainExecutor);
        // 投融资金额
        final CompletableFuture<Void> investFuture = CompletableFuture.runAsync(() -> {
            Map<String, BigDecimal> financeMap = financeService.getStatisticByChain(chainId, cityId, areaId);
            BigDecimal financeAmount = financeMap.get("financeAmount").multiply(new BigDecimal(10000)).setScale(0, RoundingMode.HALF_UP);
            resultList.add(new CommonIndexBO("投融资", financeAmount, "万元"));
            BigDecimal projectAmount = projectService.countSubsidy(chainId, null, cityId, areaId, null);
            resultList.add(new CommonIndexBO("项目补助资金", projectAmount, "万元"));
            resultList.add(new CommonIndexBO("资金规模", financeAmount.add(projectAmount), "万元"));
        }, chainExecutor);
        TaskUtil.get(companyFuture, platformFuture, patentFuture, personFuture, nodeFuture,
                riskFuture, projectFuture, investFuture);
        return resultList;
    }

    @Override
    public Pair<BigDecimal, BigDecimal> getFusionIndexByChainIdAndRegionId(String chainId, String regionId) {
        List<IndexFusion> fusionHistory = indexFusionService.list(Arrays.asList(FusionIndexEnum.COMPOSITE.getIndexId()), chainId,
                null, regionId, true, false);
        if (CollectionUtils.isEmpty(fusionHistory)) {
            return new ImmutablePair<>(null, null);
        }
        IndexFusion currentFusion = fusionHistory.get(fusionHistory.size() - 1);
        if (fusionHistory.size() < 2) {
            return new ImmutablePair<>(currentFusion.getData(), null);
        }
        IndexFusion lastFusion = fusionHistory.get(fusionHistory.size() - 2);
        BigDecimal increase = currentFusion.getData().subtract(lastFusion.getData())
                .divide(lastFusion.getData(), 3, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
        return new ImmutablePair<>(currentFusion.getData(), increase);
    }
}
